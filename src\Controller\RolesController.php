<?php
declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Routing\Router;
use App\Model\Table\ModulesTable;
use App\Model\Table\PermissionsTable;
use Cake\View\JsonView;
use Cake\Core\Configure;

/**
 * Roles Controller
 *
 * @property \App\Model\Table\RolesTable $Roles
 */
class RolesController extends AppController
{
    protected $Modules;
    protected $Permissions;
    protected $RoleCountryPermissions;

    public function initialize(): void
    {
        parent::initialize();
        $this->Modules = $this->fetchTable('Modules');
        $this->Permissions = $this->fetchTable('Permissions');
        $this->RoleCountryPermissions = $this->fetchTable('RoleCountryPermissions');
        $this->viewBuilder()->setLayout('admin');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'login', 'logout']);
    }

    public function index()
    {

        $query = $this->Roles->find();

        $query->select([
                'Roles.id',
                'Roles.name',
                'Roles.status',
                'user_count' => $query->func()->count('Users.id')
            ])
            ->leftJoinWith('Users', function ($q) {
                return $q->where(['Users.status' => 'A']);
             })
            ->group(['Roles.id'])
            ->order(['Roles.name' => 'ASC']);

        $query->where(['Roles.status !=' => 'D']);
        $roles = $query->toArray();

        $title = 'Manage Roles';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $this->set(compact('roles', 'title', 'status', 'statusMap'));
    }

    public function view($id = null)
    {

        $role = $this->Roles->get($id, [
            'contain' => ['Permissions']
        ]);

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);
        // echo "<pre>"; print_r($moduleHierarchy); die;

        $permissionsData = $this->Permissions->find('all', [
            'conditions' => ['Permissions.role_id' => $id]
        ])->toArray();

        $permissions = [];
        foreach ($permissionsData as $permission) {
            $permissions[$permission->module_id] = [
                'can_view' => !empty($permission->can_view) ? $permission->can_view : 0,
                'can_create' => !empty($permission->can_create) ? $permission->can_create : 0,
                'can_edit' => !empty($permission->can_edit) ? $permission->can_edit : 0,
                'can_approve' => !empty($permission->can_approve) ? $permission->can_approve : 0,
                'store_based' => !empty($permission->store_based) ? $permission->store_based : 0,
                'can_delete' => !empty($permission->can_delete) ? $permission->can_delete : 0,
            ];
        }

        $title = 'Role | View';
        $this->set(compact('role', 'modules', 'permissions', 'title'));
    }

    public function add()
    {
        $role = $this->Roles->newEmptyEntity();
        if ($this->request->is('post')) {

            $role = $this->Roles->patchEntity($role, $this->request->getData());
            $roleId = $this->Roles->save($role);
            if ($roleId) {
                $id = $roleId->id;
                $permissionsData = $this->request->getData('Permission');
                foreach ($permissionsData as $moduleId => $permissions) {
                    $permission = $this->Permissions->newEntity([
                        'role_id' => $id,
                        'module_id' => $moduleId,
                        'can_view' => !empty($permissions['_read']) ? 1 : 0,
                        'can_create' => !empty($permissions['_create']) ? 1 : 0,
                        'can_edit' => !empty($permissions['_update']) ? 1 : 0,
                        'can_approve' => !empty($permissions['_approve']) ? 1 : 0,
                        'store_based' => !empty($permissions['_store']) ? 1 : 0,
                        'can_delete' => !empty($permissions['_delete']) ? 1 : 0
                    ]);
                    $this->Permissions->save($permission);
                }

                // Handle country permissions - only if user is master admin
                $user = $this->Authentication->getIdentity();
                $isMasterAdmin = $user && $user->role_id == Configure::read('Constants.SUPER_ADMIN_ROLE_ID');

                if ($isMasterAdmin) {
                    $countryAccessType = $this->request->getData('country_access_type', 'all');
                    $selectedCountries = $this->request->getData('selected_countries', []);

                    $this->Roles->setCountryAccess($id, $countryAccessType, $selectedCountries);
                }

                $this->Flash->success(__('The role has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The role could not be saved. Please, try again.'));
        }

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);

        // Get countries for dropdown
        $countries = $this->Countries->getCountriesForDropdown();

        // Check if current user is master admin
        $user = $this->Authentication->getIdentity();
        $isMasterAdmin = $user && $user->role_id == Configure::read('Constants.SUPER_ADMIN_ROLE_ID');

        $title = 'Role | Add';
        $this->set(compact('role', 'title', 'modules', 'countries', 'isMasterAdmin'));
    }

    private function buildHierarchy($modules, $parentId = null)
    {
        $hierarchy = [];
        foreach ($modules as $module) {
            if ($module->parent_id === $parentId) {
                $children = $this->buildHierarchy($modules, $module->id);
                if ($children) {
                    $module->children = $children;
                }
                $hierarchy[] = $module;
            }
        }
        return $hierarchy;
    }

    public function edit($id = null)
    {
        // Check if this is a protected role (Super Admin)
        $protectedRoleIds = Configure::read('Constants.PROTECTED_ROLE_IDS');
        if (in_array($id, $protectedRoleIds)) {
            $this->Flash->error(__('Super Admin role cannot be modified.'));
            return $this->redirect(['action' => 'index']);
        }

        $role = $this->Roles->get($id, [
            'contain' => ['Permissions', 'RoleCountryPermissions.Countries']
        ]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $permissionsData = $this->request->getData('Permission');

            $role = $this->Roles->patchEntity($role, $this->request->getData());

            if ($this->Roles->save($role)) {
                $this->Permissions->deleteAll(['role_id' => $id]);
                foreach ($permissionsData as $moduleId => $permissions) {
                    $permission = $this->Permissions->newEntity([
                        'role_id' => $id,
                        'module_id' => $moduleId,
                        'can_view' => !empty($permissions['_read']) ? 1 : 0,
                        'can_create' => !empty($permissions['_create']) ? 1 : 0,
                        'can_edit' => !empty($permissions['_update']) ? 1 : 0,
                        'can_approve' => !empty($permissions['_approve']) ? 1 : 0,
                        'store_based' => !empty($permissions['_store']) ? 1 : 0,
                        'can_delete' => !empty($permissions['_delete']) ? 1 : 0
                    ]);
                    $this->Permissions->save($permission);
                }

                // Handle country permissions - only if user is master admin
                $user = $this->Authentication->getIdentity();
                $isMasterAdmin = $user && $user->role_id == Configure::read('Constants.SUPER_ADMIN_ROLE_ID');

                if ($isMasterAdmin) {
                    $countryAccessType = $this->request->getData('country_access_type', 'all');
                    $selectedCountries = $this->request->getData('selected_countries', []);

                    $this->Roles->setCountryAccess($id, $countryAccessType, $selectedCountries);
                }
            }

            $this->Flash->success(__('The permissions have been saved.'));
            return $this->redirect(['action' => 'index']);
        }

        $modules = $this->Modules->find('all')->toArray();
        $moduleHierarchy = $this->buildHierarchy($modules);
        // echo "<pre>"; print_r($moduleHierarchy); die;

        $permissionsData = $this->Permissions->find('all', [
            'conditions' => ['Permissions.role_id' => $id]
        ])->toArray();

        $permissions = [];
        foreach ($permissionsData as $permission) {
            $permissions[$permission->module_id] = [
                'can_view' => !empty($permission->can_view) ? $permission->can_view : 0,
                'can_create' => !empty($permission->can_create) ? $permission->can_create : 0,
                'can_edit' => !empty($permission->can_edit) ? $permission->can_edit : 0,
                'can_approve' => !empty($permission->can_approve) ? $permission->can_approve : 0,
                'store_based' => !empty($permission->store_based) ? $permission->store_based : 0,
                'can_delete' => !empty($permission->can_delete) ? $permission->can_delete : 0,
            ];
        }

        // Get countries for dropdown
        $countries = $this->Countries->getCountriesForDropdown();

        // Get current role's country permissions
        $roleCountryPermissions = [];
        $currentCountryAccessType = $role->country_access_type ?? 'all';

        if (!empty($role->role_country_permissions)) {
            foreach ($role->role_country_permissions as $permission) {
                if ($permission->can_access) {
                    $roleCountryPermissions[] = $permission->country_id;
                }
            }
        }

        // Check if current user is master admin
        $user = $this->Authentication->getIdentity();
        $isMasterAdmin = $user && $user->role_id == Configure::read('Constants.SUPER_ADMIN_ROLE_ID');

        $title = 'Role | Edit';
        $this->set(compact('role', 'modules', 'permissions', 'title', 'countries', 'roleCountryPermissions', 'currentCountryAccessType', 'isMasterAdmin'));
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        // Check if this is a protected role (Super Admin)
        $protectedRoleIds = Configure::read('Constants.PROTECTED_ROLE_IDS');
        if (in_array($id, $protectedRoleIds)) {
            $response = ['success' => false, 'message' => 'Super Admin role cannot be deleted.'];
        } else {
            try {
                $record = $this->Roles->get($id);
                $record->status = 'D';
                if ($this->Roles->save($record)) {
                    $response = ['success' => true, 'message' => 'The role has been marked as deleted.'];
                }
            } catch (\Exception $e) {
                $response['message'] = $e->getMessage();
            }
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function filterSearch()
    {
        $status = $this->request->getQuery('filterStatus');

        if ($this->request->is('ajax')) {

            $query = $this->Roles->find();

            if ($status) {
                $query->where(['Roles.status' => $status]);
            } else {
                $query->where(['Roles.status' => 'A']);
            }
            $roles = [];
            $i = 1;
            foreach ($query as $role) {

                $statusMap = [
                    'A' => ['label' => 'Active', 'class' => 'col-green'],
                    'I' => ['label' => 'Inactive', 'class' => 'col-blue'],
                    'D' => ['label' => 'Deleted', 'class' => 'col-red']
                ];

                $status = $statusMap[$role->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                $roles[] = [
                    'id' => $i,
                    'name' => $role->name,
                    'description' => $role->description,
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                    'actions' => '<a href="' . Router::url(['controller' => 'Brands', 'action' => 'view', $role->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                        '<a href="' . Router::url(['controller' => 'Roles', 'action' => 'edit', $role->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="far fa-edit m-r-10"></i></a>' .
                        '<a href="' . Router::url(['controller' => 'Roles', 'action' => 'delete', $role->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
                ];
                $i++;
            }

            $this->set([
                'brands' => $roles,
                '_serialize' => ['roles'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $roles]));
        }

        return null;
    }
}