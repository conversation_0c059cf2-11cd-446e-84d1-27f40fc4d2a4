<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Core\Configure;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $contentpages
 */
class ContentPagesController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $ContentImages;
    protected $Countries;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->ContentImages = $this->fetchTable('ContentImages');
        $this->Countries = $this->fetchTable('Countries');
    }
    
    public function index()
    {
        $query = $this->ContentPages->find()
            ->contain(['Countries']) // Add Countries association to show country name
            ->where(['ContentPages.status IN' => ['A', 'I']])
            ->order(['ContentPages.title' => 'ASC']);

        // Apply role-based country filtering
        $query = $this->applyRoleBasedCountryFilter($query, 'ContentPages.country_id');

        $content_pages = $query->toArray();

        $this->set(compact('content_pages'));
    }

    /**
     * View method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $content = $this->ContentPages->get($id, contain: [
            'ContentImages'
        ]);

        if (!empty($content->content_images)) {
            foreach ($content->content_images as &$image) {
                $image->image = $this->Media->getCloudFrontURL($image->image);
            }
        }

        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');

        $this->set(compact('content', 'dateFormat', 'timeFormat'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $content = $this->ContentPages->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            // Handle country_id assignment based on current country filter
            $selectedCountryId = $this->getCurrentCountryFilter();
            if ($selectedCountryId) {
                // Use the country selected in header dropdown
                $data['country_id'] = $selectedCountryId;
            } else {
                // If "All Countries" is selected, force user to select a specific country first
                $this->Flash->error(__('Please select a specific country from the header dropdown before adding a content page. Content pages must be assigned to a specific country.'));

                // Redirect back to add form
                return $this->redirect(['action' => 'add']);
            }

            $content = $this->ContentPages->patchEntity($content, $data);

            if ($this->ContentPages->save($content)) {

                $contentId = $content->id;

                $uploadedImages = !empty($this->request->getData('content_image')) ? $this->handleFileUploads($contentId) : [];

                if (!empty($uploadedImages)) {
                    $firstImage = $uploadedImages[0];
                    $content->image = $firstImage;
                    $this->ContentPages->save($content);
                }

                $this->Flash->success(__('The CMS content has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The CMS content could not be saved. Please, try again.'));
        }

        $menus = ['Website Pages' => 'Website Pages', 'Policies' => 'Policies', 'General' => 'General', 'Testimonials' => 'Testimonials'];

        $imageSize = Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE');
        $webImageMinWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.CONTENT_WEB_IMAGE_TYPE');

        $file_acceptance_msg = __('Only '.$webImageType.' files are allowed. Max size: '.$imageSize.'MB. Dimensions: '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight.'');

        $this->set([
            'webImageMinWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT'),
            'webImageSize' => Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE'),
        ]);

        // Get selected country for display
        $selectedCountryId = $this->request->getSession()->read('Admin.selectedCountryId');
        $selectedCountry = null;
        if ($selectedCountryId) {
            $selectedCountry = $this->Countries->getCountryById($selectedCountryId);
        }

        $this->set(compact('content', 'menus', 'file_acceptance_msg', 'selectedCountry'));
    }

    private function handleFileUploads($contentId)
    {
        $files = $this->request->getData('content_image');
        $uploadedImages = [];

        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');

                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.CONTENT_PAGES');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $contentImage = $this->ContentPages->ContentImages->newEmptyEntity();
                        $contentImage->content_page_id = $contentId;
                        $contentImage->image = $folderPath . $imageFile;

                        if ($this->ContentPages->ContentImages->save($contentImage)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    /**
     * Edit method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function edit($id = null)
    {

        $content = $this->ContentPages->get($id, contain: ['ContentImages', 'Countries']);

        // Check if user can access this content page's country
        if (!$this->canUserAccessCountry($content->country_id)) {
            $this->Flash->error(__('You do not have permission to access this content page.'));
            return $this->redirect(['action' => 'index']);
        }

        // echo "<pre>"; print_r($content);die;
        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $content = $this->ContentPages->patchEntity($content, $data);

            $deletedImages = json_decode($this->request->getData('deleted_images'), true);
            if (!empty($deletedImages)) {
                $this->ContentImages->deleteAll(['id IN' => $deletedImages]);
            }

            if ($this->ContentPages->save($content)) {

                $contentId = $content->id;

                $uploadedImages = !empty($this->request->getData('content_image')) ? $this->handleFileUploads($contentId) : [];

                if (!empty($uploadedImages)) {
                    $firstImage = $uploadedImages[0];
                    $content->image = $firstImage;
                    $this->ContentPages->save($content);
                }

                $this->Flash->success(__('The CMS content has been saved.'));

                return $this->redirect(['action' => 'index']);
            }

            $this->Flash->error(__('The CMS content could not be saved. Please, try again.'));
        }

        if (!empty($content->content_images)) {
            foreach ($content->content_images as &$image) {
                $image->image = $this->Media->getCloudFrontURL($image->image);
            }
        }

        $menus = ['Website Pages' => 'Website Pages', 'Policies' => 'Policies', 'General' => 'General', 'Testimonials' => 'Testimonials'];

        $imageSize = Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE');
        $webImageMinWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.CONTENT_WEB_IMAGE_TYPE');

        $file_acceptance_msg = __('Only '.$webImageType.' files are allowed. Max size: '.$imageSize.'MB. Dimensions: '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight.'');

        $this->set([
            'webImageMinWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.CONTENT_WEB_IMAGE_MAX_HEIGHT'),
            'webImageSize' => Configure::read('Constants.CONTENT_WEB_IMAGE_SIZE'),
        ]);

        $this->set(compact('content', 'menus', 'file_acceptance_msg'));

    }

    /**
     * Delete method
     *
     * @param string|null $id Showroom id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The content page could not be deleted. Please, try again.'];

        try {
            $record = $this->ContentPages->get($id);

            // Check if user can access this content page's country
            if (!$this->canUserAccessCountry($record->country_id)) {
                $response = ['success' => false, 'message' => 'You do not have permission to delete this content page.'];
            } else {
                $record->status = 'D';

                if ($this->ContentPages->save($record)) {
                    $response = ['success' => true, 'message' => 'The content page has been marked as deleted.'];
                }
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }
}
