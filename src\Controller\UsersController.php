<?php

declare(strict_types=1);

namespace App\Controller;
use App\Controller\AppController;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\Time;
use Cake\I18n\FrozenTime;
use App\Mailer\UserMailer;
use Authentication\PasswordHasher\DefaultPasswordHasher;


/**
 * Users Controller
 *
 */
class UsersController extends AppController
{

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'login', 'logout']);
    }

    public function login()
    {
        $this->viewBuilder()->setLayout('auth');
        $this->request->allowMethod(['get', 'post']);
        $result = $this->Authentication->getResult();

        // if ($result->isValid()) {
        //     return $this->redirect(['controller' => 'Dashboards', 'action' => 'index']);
        // }

        if ($this->request->is('post')) {
            $user = $this->Authentication->getResult()->getData();
            if ($user) {
                if ($user->user_type == 'Customer') {
                    $this->Flash->error(__('Customers do not have permission to access this resource.'));
                } else if ($user->role_id == null) {
                    $this->Flash->error(__('User has not been assigned any role.'));
                } else {
                    return $this->redirect(['controller' => 'Dashboards', 'action' => 'index']);
                }
            } else {
                $this->Flash->error(__('Invalid email or password.'));
            }
        }
    }

    public function logout()
    {
        $result = $this->Authentication->getResult();

        if ($result->isValid()) {
            $this->Authentication->logout();
            return $this->redirect(['controller' => 'Users', 'action' => 'login']);
        } else {
            $this->Flash->error('Logout failed. Please try again.');
            return $this->redirect(['controller' => 'Users', 'action' => 'login']);
        }
    }

    public function index()
    {

        $query = $this->Users->find()
            ->contain(['Roles'])->order(['Users.first_name' => 'ASC']);

        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $query->where(['Users.role_id' => $roleId]);
        }

        $query->where(['Users.status !=' => 'D']);

        $users = $query->toArray();

        $title = 'Manage Users';
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $this->set(compact('users', 'title', 'status', 'statusMap'));
    }

    public function view($id = null)
    {
        $user = $this->Users->get($id, ['contain' => ['Roles']]);

        $title = 'User | View';
        $this->set(compact('user', 'title'));
    }

    public function add()
    {
        $curUser = $this->Users->newEmptyEntity();
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $data['mobile_no'] = $data['mobile_full'];
            $curUser = $this->Users->patchEntity($curUser, $data);
            $curUser->user_type = 'Admin';
            $plainPassword = Security::randomString(10);
            $curUser->password = $plainPassword;

            // Validate uniqueness of email
            if ($this->Users->save($curUser)) {

                $to = trim($data['email']);
                $subject = "Welcome to Babiken";
                $template = "welcome_user";

                $viewVars = array('username' => $curUser->first_name . ' ' . $curUser->last_name, 'email' => $data['email'], 'password' => $plainPassword);

                $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                if ($send_email) {
                    $this->Flash->success('The user has been created and an email has been sent with their login credentials.');
                } else {
                    $this->Flash->error('The user could not be saved. Please, try again.');
                }
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('Unable to add the user. Please, try again.'));
        }

        $roles = $this->Users->Roles->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A']);

        $departments = $this->Users->find()
            ->select(['department'])
            ->distinct(['department'])
            ->where(['department IS NOT' => null, 'department !=' => ''])
            ->order(['department' => 'ASC'])
            ->toArray();

        $title = 'User | Add';
        $this->set(compact('curUser', 'roles', 'title', 'departments'));
    }

    public function edit($id = null)
    {
        $curUser = $this->Users->get($id, ['contain' => []]);

        if ($this->request->is(['patch', 'post', 'put'])) {

            $data = $this->request->getData();

            $data['mobile_no'] = $data['mobile_full'];

            $curUser = $this->Users->patchEntity($curUser, $data);
            if ($this->Users->save($curUser)) {
                $this->Flash->success(__('The user has been updated.'));
                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The user could not be saved. Please, try again.'));
        }

        $roles = $this->Users->Roles->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A']);

        // echo "<pre>"; print_r($roles); die;

        $departments = $this->Users->find()
            ->select(['department'])
            ->distinct(['department'])
            ->where(['department IS NOT' => null, 'department !=' => ''])
            ->order(['department' => 'ASC'])
            ->toArray();

        $title = 'User | Edit';
        $this->set(compact('curUser', 'roles', 'title', 'departments'));
    }

    public function editProfile($id = null)
    {
        $curUser = $this->Users->get($id, ['contain' => []]);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();

            $data['mobile_no'] = $data['mobile_full'];

            if (!empty($data['new_password']) && $data['new_password'] === $data['confirm_new_password']) {
                $data['password'] = $data['new_password'];
            } elseif (!empty($data['new_password']) || !empty($data['confirm_new_password'])) {
                $this->Flash->error(__('New Password and Confirm New Password do not match.'));
                return $this->redirect(['action' => 'editProfile', $id]);
            } else {
                unset($data['new_password'], $data['confirm_new_password']);
            }

            $curUser = $this->Users->patchEntity($curUser, $data);

            if ($this->Users->save($curUser)) {
                $this->Flash->success(__('The user profile has been updated.'));
                return $this->redirect(['action' => 'editProfile', $id]);
            }
            $this->Flash->error(__('The user could not be saved. Please, try again.'));
        }

        $roles = $this->Users->Roles->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A']);

        $departments = $this->Users->find()
            ->select(['department'])
            ->distinct(['department'])
            ->where(['department IS NOT' => null, 'department !=' => ''])
            ->order(['department' => 'ASC'])
            ->toArray();

        $title = 'User | Edit Profile';
        $this->set(compact('curUser', 'roles', 'title', 'departments'));
    }

    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        $response = ['success' => false, 'message' => 'The user could not be deleted. Please, try again.'];

        try {
            $record = $this->Users->get($id);

            // Check if this user has a super admin role
            $superAdminRoleId = Configure::read('Constants.SUPER_ADMIN_ROLE_ID');
            if ($record->role_id == $superAdminRoleId) {
                $response = ['success' => false, 'message' => 'Super Admin user cannot be deleted.'];
            } else {
                $record->status = 'D';

                if ($this->Users->save($record)) {
                    $response = ['success' => true, 'message' => 'The user has been marked as deleted.'];
                }
            }
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }

    public function forgotPassword()
    {
        $this->viewBuilder()->setLayout('auth');

        if ($this->request->is('post')) {
            $email = $this->request->getData('email');
            $user = $this->Users->findByEmail($email)->first();

            if ($user) {
                $token = Security::hash(Security::randomBytes(25));
                $user->password_reset_token = $token;
                $user->token_created_at = date('Y-m-d H:i:s');

                if ($this->Users->save($user)) {
                    $resetLink = Router::url(['controller' => 'Users', 'action' => 'resetPassword', $token], true);

                    $to = trim($user->email);
                    $subject = "Reset your Password for Babiken";
                    $template = "forgot_password";

                    $viewVars = array('resetLink' => $resetLink, 'token' => $token, 'userId' => $user->id, 'username' => $user->first_name . ' ' . $user->last_name, 'datetime' => date('d-m-Y H:i:s'));

                    $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                    if ($send_email) {
                        $this->Flash->success('Please check your email to reset your password.');
                    } else {
                        $this->Flash->error('Unable to send the password reset email. Please try again.');
                    }
                } else {
                    $this->Flash->error('Unable to send the password reset email. Please try again.');
                }
            } else {
                $this->Flash->error('Email address not found. Please try again.');
            }
        }
    }

    public function resetPassword($token = null)
    {
        $this->viewBuilder()->setLayout('auth');

        if (!$token) {
            $this->Flash->error('Invalid token. Please try again.');
            return $this->redirect(['action' => 'forgotPassword']);
        }

        $user = $this->Users->find('all', [
            'conditions' => [
                'password_reset_token' => $token,
                'token_created_at >' => FrozenTime::now()->modify('-1 hours')
            ]
        ])->first();

        if (!$user) {
            $this->Flash->error('Invalid or expired token. Please try again.');
            return $this->redirect(['action' => 'forgotPassword']);
        }

        if ($this->request->is(['post', 'put'])) {
            $password = $this->request->getData('password');
            $confirmPassword = $this->request->getData('confirm_password');

            if ($password !== $confirmPassword) {
                $this->Flash->error('Passwords do not match. Please try again.');
            } else {
                $user->password = $password;
                $user->password_reset_token = null;
                $user->token_created_at = null;

                if ($this->Users->save($user)) {
                    $this->Flash->success('Your password has been updated successfully.');
                    return $this->redirect(['action' => 'login']);
                } else {
                    $this->Flash->error('Unable to update your password. Please try again.');
                }
            }
        }

        $this->set(compact('token'));
    }

    public function filterSearch()
    {
        $status = $this->request->getQuery('filterStatus');

        if ($this->request->is('ajax')) {

            $query = $this->Users->find()
                ->contain(['Roles']);

            if ($status) {
                $query->where(['Users.status' => $status]);
            } else {
                $query->where(['Users.status' => 'A']);
            }

            $users = [];
            $i = 1;
            foreach ($query as $user) {

                $statusMap = [
                    'A' => ['label' => 'Active', 'class' => 'col-green'],
                    'I' => ['label' => 'Inactive', 'class' => 'col-blue'],
                    'D' => ['label' => 'Deleted', 'class' => 'col-red']
                ];

                $status = $statusMap[$user->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];

                $users[] = [
                    'id' => $i,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                    'role' => $user->role->name,
                    'status' => '<div class="badge-outline ' . h($status['class']) . '">' . h($status['label']) . '</div>',
                    'actions' => '<a href="' . Router::url(['controller' => 'Users', 'action' => 'view', $user->id], true) . '" class="btn-view" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a> ' .
                        '<a href="' . Router::url(['controller' => 'Users', 'action' => 'edit', $user->id], true) . '" class="btn-edit" data-toggle="tooltip" title="Edit"><i class="far fa-edit m-r-10"></i></a>' .
                        '<a href="' . Router::url(['controller' => 'Users', 'action' => 'delete', $user->id], true) . '" class="delete-btn" data-toggle="tooltip" title="Delete"><i class="far fa-trash-alt"></i></a>'
                ];
                $i++;
            }

            $this->set([
                'users' => $users,
                '_serialize' => ['users'],
            ]);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['data' => $users]));
        }

        return null;
    }

    public function verifyEmail($user_id, $token)
    {

        if (!$token) {
            $this->Flash->error(__('Token is missing'));
        }

        $this->viewBuilder()->setLayout('auth');
        $user = $this->Users->find()
            ->where(['id' => $user_id, 'email_verify_token' => $token])
            ->first();

        if ($user) {
            $user->is_email_verified = 1;
            $user->email_verify_token = null;

            if ($this->Users->save($user)) {
                $this->Flash->success(__('Your email has been successfully verified.'));
            } else {
                $this->Flash->error(__('Email verification failed.'));
            }
        } else {
            $this->Flash->error(__('Invalid verification link or the link has expired.'));
        }

        $this->set(compact('token'));
    }

    public function getManagerDetails($managerId)
    {
        $this->request->allowMethod(['get']);

        $manager = $this->Users->find()
            ->where(['Users.id' => $managerId])
            ->first();

        if ($manager) {
            $mobileNo = $manager->mobile_no;
            $countryCode = '';
            $phoneNumber = '';

            if (strpos($mobileNo, '+91') === 0) {
                $countryCode = '91';
                $phoneNumber = substr($mobileNo, 3);
            } elseif (strpos($mobileNo, '+225') === 0) {
                $countryCode = '225';
                $phoneNumber = substr($mobileNo, 4);
            } else {

                $countryCode = '225';
                $phoneNumber = substr($mobileNo, 4);
            }
            $managerDetails = [
                'full_name' => $manager->first_name . ' ' . $manager->last_name,
                'email' => $manager->email,
                'phone' => $phoneNumber,
                'country_code' => $countryCode
            ];

            $response = [
                'status' => 'success',
                'managerDetails' => $managerDetails
            ];
        } else {
            $response = [
                'status' => 'error',
                'message' => __('Manager not found')
            ];
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }

    public function fetchUsersbyRole()
    {
        $this->request->allowMethod(['get']);
        $roleId = $this->request->getQuery('role_id');

        $response = ['status' => 'error', 'message' => __('Invalid Role ID.')]; // Default response

        if ($roleId) {
            $users = $this->Users->find('list', [
                'keyField' => 'id',
                'valueField' => function ($row) {
                    return $row->first_name . ' ' . $row->last_name;
                }
            ])
                ->where(['role_id' => $roleId, 'status' => 'A'])
                ->order(['first_name' => 'ASC'])
                ->toArray();


            if (!empty($users)) {
                $response = [
                    'status' => 'success',
                    'users' => $users
                ];
            } else {
                $response['message'] = __('No users found for the selected role.');
            }
        }

        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }
}
