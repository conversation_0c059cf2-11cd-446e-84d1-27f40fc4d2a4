<?php
/**
 * Contact Us Page Template
 *
 * @var \App\View\AppView $this
 */

// Set page title

// Get session instance
$session = $this->request->getSession();
$currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
$country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';

$this->assign('title', __('Contact Us'));
$this->assign('meta_description', __('Get in touch with us. We are here to help you with any questions or concerns.'));
$this->assign('meta_keywords', __('contact, support, help, customer service'));
?>

    <section class=" my-2 my-lg-5 ">
        <div class="container">
            <nav aria-label="breadcrumb" class="">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Contact Us</li>
                    </li>
                </ol>
            </nav>
        </div>
    </section>

    <section class="contactus-form">
        <div class="container">
            <small class="">Get Started</small>
            <div class="d-flex  align-items-center">
                <h1 class="section-title mt-4">Get in touch with us. </br>We're here to assist you.</h1>
            </div>
        </div>
    </section>


<!-- Contact Form -->
<section class="contact-section text-center mt-5">
    <div class="container">
        <form id="contact" method="post" autocomplete="off">
            <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]); ?>
            <div class="row g-3">
                <div class="col-md-4">
                    <input type="text" name="name" class="form-control" placeholder="Your Name" required
                        pattern="^[A-Za-z\s]+$" title="Name should contain only alphabets and spaces">
                </div>
                <div class="col-md-4">
                    <input type="email" name="email" class="form-control" placeholder="Your Email" required
                        pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" title="Please enter a valid email address">
                </div>
                <div class="col-md-4">
                    <input type="text" name="phone" class="form-control" placeholder="Phone Number" required
                        pattern="^\d{8,13}$" minlength="8" maxlength="13" title="Please enter a valid phone number (8-13 digits)">
                </div>
                <div class="col-md-12 mt-4">
                    <select name="inquiry_type" class="form-control" required>
                        <option value="">Select Inquiry Type</option>
                        <option value="Order Related">Order Related</option>
                        <option value="Product Related">Product Related</option>
                        <option value="Complaint">Complaint</option>
                        <option value="Issue On Server">Issue on Server</option>
                    </select>
                </div>
                <div class="col-12 mt-4">
                    <textarea name="message" class="form-control" rows="6" placeholder="Your Message" required></textarea>
                </div>
                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-custom btn-primary">
                        Leave us a Message →
                    </button>
                </div>
                <div class="col-12 mt-3" id="contact-response"></div>
            </div>
        </form>
    </div>
</section>

    <!-- Contact Info -->
    <section class="contact-info">
        <div class="container">
            <div class="row text-center text-md-start">
                <div class="col-md-4 mb-4">
                    <small class="">Contact Info</small>
                    <h2 class=" mt-4 fw-bold">We are always happy<br>to assist you</h2>
                </div>
                <div class="col-md-4 mb-4">
                    <h6 class="fw-bold">Email Address</h6>
                    <hr />
                    <p><a href="mailto:<?= $session->read('siteSettings.support_email') ?>" class="text-dark fw-bold"><?= $session->read('siteSettings.support_email') ?></a></p>
                    <p>Assistance hours:<br><?= __('Sat - Thu:') ?> <?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?> EST</p>
                </div>
                <div class="col-md-4 mb-4">
                    <h6 class="fw-bold">Number</h6>
                    <hr />
                    <p><a href="tel:<?= $session->read('siteSettings.contact_no') ?>" class="text-dark fw-bold"><?= $session->read('siteSettings.contact_no') ?></a></p>
                    <p>Assistance hours:<br><?= __('Sat - Thu:') ?> <?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?> EST</p>
                </div>
            </div>
        </div>
    </section>

<?php $this->start('add_js'); ?>
<script>
$(function() {
    // Add custom name validation method (alphabets and spaces only)
    if (typeof $.validator === 'function') {
        $.validator.addMethod("alphaOnly", function(value, element) {
            return this.optional(element) || /^[A-Za-z\s]+$/.test(value);
        }, "Name should contain only alphabets and spaces.");

        // Custom phone validation: only digits, min 8, max 13
        $.validator.addMethod("phoneCustom", function(value, element) {
            return this.optional(element) || (/^\d{8,13}$/.test(value));
        }, "Please enter a valid phone number (8-13 digits).");

        // Custom email validation (in addition to built-in)
        $.validator.addMethod("emailCustom", function(value, element) {
            return this.optional(element) || /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
        }, "Please enter a valid email address.");
    }

    if ($('#contact').data('validator')) {
        $('#contact').removeData('validator');
    }
    $('#contact').validate({
        rules: {
            name: { required: true, alphaOnly: true },
            email: { required: true, email: true, emailCustom: true },
            phone: { required: true, phoneCustom: true, minlength: 8, maxlength: 13 },
            inquiry_type: { required: true },
            message: { required: true }
        },
        messages: {
            name: {
                required: "Please enter your name",
                alphaOnly: "Name should contain only alphabets and spaces"
            },
            email: {
                required: "Please enter your email address",
                email: "Please enter a valid email address",
                emailCustom: "Please enter a valid email address"
            },
            phone: {
                required: "Please enter your phone number",
                phoneCustom: "Please enter a valid phone number (8-13 digits)",
                minlength: "Phone number must be at least 8 digits",
                maxlength: "Phone number must not exceed 13 digits"
            },
            inquiry_type: "Please select an inquiry type",
            message: "Please enter your message"
        },
        errorElement: 'div',
        errorClass: 'invalid-feedback',
        highlight: function(element) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function(element) {
            $(element).removeClass('is-invalid');
        },
        errorPlacement: function(error, element) {
            if (element.parent('.input-group').length) {
                error.insertAfter(element.parent());
            } else {
                error.insertAfter(element);
            }
        },
        submitHandler: function(form) {
            $.ajax({
                url: "<?= $this->Url->build(['controller' => 'Home', 'action' => 'contactUs']) ?>",
                type: "POST",
                data: $(form).serialize(),
                headers: {
                    'X-CSRF-Token': $('input[name="_csrfToken"]').val()
                },
                dataType: "json",
                beforeSend: function() {
                    $('#contact-response').html('');
                },
                success: function(response) {
                    if (response.status === 'success') {
                        showToastMessage(response.message || 'Contact form submitted successfully!', 'success');
                        $('#contact')[0].reset();
                        $('#contact .form-control').removeClass('is-invalid');
                        $('#contact-response').html('');
                    } else {
                        showToastMessage(response.message || 'There was an error submitting the form.', 'error');
                        $('#contact-response').html('');
                    }
                },
                error: function() {
                    showToastMessage('There was an error submitting the form. Please try again.', 'error');
                    $('#contact-response').html('');
                }
            });
            return false;
        }
    });
});
</script>
<?php $this->end(); ?>