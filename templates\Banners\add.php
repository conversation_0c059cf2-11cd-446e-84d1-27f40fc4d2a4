<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Banner $banner
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <style>
        input,
        select,
        textarea {
            width: 300px;
            padding: 5px;
            margin-bottom: 10px;
        }

        .web-media-container {
            position: relative;
            width: 50%;
        }

    </style>
</head>
<?php $this->end(); ?>

<body>
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Banners', 'action' => 'index']) ?>">
                    <?= __('Banners') ?>
                </a>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Add') ?>
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
    </div>


    <div class="section-body">
        <div class="container-fluid">
            <div class="card">
                <?php echo $this->Form->create($banner, ['id' => 'addBannersForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
                <h6 class="m-b-20" style="color: #206331">
                    <?= __('Add Banner') ?>
                </h6>
                <div class="form-group row">
                    <label for="title" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Title') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'title',
                            'placeholder' => __('Enter Banner Title'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="title_ar" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Title (Arabic)') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title_ar', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'title_ar',
                            'placeholder' => __('Enter Banner Title (Arabic)'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>



                <div class="form-group row">
                    <label for="web-media" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Image') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('web_media_file', [
                            'type' => 'file',
                            'class' => 'form-control',
                            'id' => 'web-media',
                            'placeholder' => __('Banner Image'),
                            'accept' => 'image/*',
                            'data-max-size' => '10MB',
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <span id="Image-suggestions" class="Image-suggestions"><small>Suggested: <?= implode(', ', $webImageTypedisp) ?> files. Max size: <?= $webImageSize ?> MB.</small></span>
                        <div class="web-media-container">

                        </div>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>



                <!-- Country Assignment Info (Read-only display) -->
                <?php
                $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId');
                if ($currentCountryFilter && isset($selectedCountry) && $selectedCountry): ?>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                    <div class="col-sm-5">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <?= __('This banner will be assigned to: <strong>{0}</strong>', h($selectedCountryName)) ?>
                            <br>
                            <small class="text-muted">
                                <?= __('To change country, use the country dropdown in the header.') ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php elseif (!$currentCountryFilter): ?>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?><sup class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= __('No country selected. Please select a country from the header dropdown before adding a banner.') ?>
                            <br>
                            <small class="text-muted">
                                <?= __('Banners must be assigned to a specific country.') ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="form-group row">
                    <label for="banner-location" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Location') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">

                        <?php echo $this->Form->control('banner_location', [
                            'type' => 'select',
                            'id' => 'banner-location',
                            'options' => $bannerloc,
                            'class' => 'form-control',
                            'label' => false,
                            'empty' => __('Select a Banner Location'),
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="banner-description"
                        class="col-sm-2 col-form-label fw-bold"><?= __('Banner Description') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('summary', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'banner-description',
                            'placeholder' => __('Banner Description'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="banner-description_ar"
                        class="col-sm-2 col-form-label fw-bold"><?= __('Banner Description (Arabic)') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('summary_ar', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'banner-description_ar',
                            'placeholder' => __('Banner Description (Arabic)'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="url-link" class="col-sm-2 col-form-label fw-bold"><?= __('URL Link') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('url_link', [
                            'type' => 'url',
                            'class' => 'form-control',
                            'id' => 'url-link',
                            'placeholder' => __('Enter Url Link'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" class="btn btn-primary" id="btnSubmit"<?= !$currentCountryFilter ? ' disabled' : '' ?>><?= __("Save") ?></button>
                        <button type="reset" class="btn btn-primary"><?= __("Reset") ?></button>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
</body>

<?php $this->append('script'); ?>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>

<script>
    $(document).ready(function() {
        // Debug: Log country information
        console.log('Current Country Filter:', <?= json_encode($currentCountryFilter) ?>);
        console.log('Selected Country ID:', <?= json_encode($selectedCountryId) ?>);
        console.log('Selected Country Name:', <?= json_encode($selectedCountryName) ?>);

        // Disable save button if no country is selected
        <?php if (!$currentCountryFilter): ?>
            $('#btnSubmit').prop('disabled', true);
        <?php endif; ?>

        $('#web-media').on('change', function() {
            let isValid = true;
            $('.web-media-container').empty();

            // Basic file validation (file type and size only)
            const file = this.files[0];
            if (file) {
                // Check file type using both MIME type and file extension
                const allowedMimeTypes = <?= json_encode($webImageType) ?>;
                const allowedExtensions = <?= json_encode($webImageTypedisp) ?>;
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                const fileMimeType = file.type;

                // Create a more robust MIME type check
                const validMimeTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/svg'
                ];

                const isValidExtension = allowedExtensions.includes(fileExtension);
                const isValidMimeType = validMimeTypes.includes(fileMimeType) || allowedMimeTypes.includes(fileMimeType);

                if (!isValidExtension && !isValidMimeType) {
                    alert('<?= __("Invalid file type. Please select a valid image file") ?> (<?= implode(", ", $webImageTypedisp) ?>)');
                    $(this).val('');
                    isValid = false;
                    return;
                }

                // Check file size
                const maxSize = <?= $webImageSize ?> * 1024 * 1024; // Convert MB to bytes
                if (file.size > maxSize) {
                    alert('<?= __("File size exceeds the maximum allowed size of") ?> <?= $webImageSize ?> MB');
                    $(this).val('');
                    isValid = false;
                    return;
                }
            }

            if (isValid && file) {
                renderMediaPreview();
            }
        });

        function renderMediaPreview() {
            $('.web-media-container').empty();
            const webFile = $('#web-media')[0].files[0];

            if (webFile) {
                const webReader = new FileReader();
                const webfileName = $('#web-media')[0].files[0].name;
                webReader.onload = function(e) {
                    $('.web-media-container').html(`<img src="${e.target.result}" alt="Banner Image Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Banner Image Preview">${webfileName}</span>
                    <button type="button" class="delete-img-btn delete-web-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                webReader.readAsDataURL(webFile);
            }
        }

        function validateForm() {
            let isValid = true;

            $('#addBannersForm').find('input[required], select[required], input[type=radio][required]').each(function() {

                if ($(this).attr('type') === 'radio') {
                    let name = $(this).attr('name');
                    if ($('input[name="' + name + '"]:checked').length === 0) {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/WebMobileBoth$/, '');
                        fieldName = fieldName.replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                } else {
                    let value = $(this).val().trim();
                    if (value === '') {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                }
            });

            if (isValid) {
                let url_link = $('#url-link').val().trim();
                if (url_link != '') {
                    let urlRegex = /^(https?:\/\/)?([\w\d\-_]+\.)+[\w\d\-_]+(\/[\w\d\-_]+)*\/?$/i;
                    if (!urlRegex.test(url_link)) {
                        $('#url-link').addClass('is-invalid');
                        let feedback = $('#url-link').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __('URL is not in the correct format') ?>.').show();
                        isValid = false;
                    } else {
                        $('#url-link').removeClass('is-invalid');
                        let feedback = $('#url-link').closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                }
            }
            return isValid;
        }

        // Check country selection before form submission
        <?php $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId'); ?>
        <?php if (!$currentCountryFilter): ?>
        // If no country is selected, disable form submission
        $('#addBannersForm').on('submit', function(e) {
            e.preventDefault();
            swal({
                title: '<?= __("Country Required") ?>',
                text: '<?= __("Please select a country from the header dropdown before adding a banner. Banners must be assigned to a specific country.") ?>',
                icon: 'warning',
                button: '<?= __("OK") ?>'
            });
            return false;
        });
        <?php endif; ?>

        $('#btnSubmit').click(function(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }
            var form = $('#addBannersForm')[0];
            form.action = "<?= $this->Url->build(['controller' => 'Banners', 'action' => 'add']) ?>";
            $('#btnSubmit').attr('disabled', true);
            form.submit();
        });
    });
    $(document).on('click', '.delete-web-media', function(event) {
        $('.web-media-container').empty();
        $('#web-media').val('');
    });
</script>
<?php $this->end(); ?>