<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

class ContentPages extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'content_category' => true,
        'content_category_identifier' => true,
        'country_id' => true,
        'title' => true,
        'title_ar' => true,
        'tag' => true,
        'description' => true,
        'description_ar' => true,
        'image' => true,
        'image_alt' => true,
        'video_url' => true,
        'content' => true,
        'content_ar' => true,
        'status' => true,
        'published' => true,
        'published_date' => true,
        'meta_title' => true,
        'meta_keyword' => true,
        'meta_description' => true,
        'url_key' => true,
        'created' => true,
        'modified' => true,
    ];
}
