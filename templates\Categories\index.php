<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Category> $categories
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item active"><?= __("Categories") ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Categories") ?></h4>
                <div class="card-header-form">
                    <div class="input-group">
                        <input type="text" class="form-control search-control" placeholder="<?= __("Search") ?>"
                            id="customSearchBox" />
                        <div class="input-group-btn">
                            <button class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <?php if ($canAdd): ?>
                            <a href="<?= $this->Url->build(['controller' => 'Categories', 'action' => 'add']) ?>"
                                class="btn m-r-15">
                                <i class="fas fa-plus"></i>
                                <?= __("Add Category") ?>
                            </a>
                        <?php endif; ?>
                        <button class="btn menu-toggle">
                            <i class="fas fa-filter"></i>
                            <?= __("Filter") ?>
                        </button>
                    </div>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="input-group m-l-25">
                    <div class="d-flex">
                        <div class="form-group d-flex align-items-center m-l-20">
                            <?php echo $this->Form->control('status', [
                                    'type' => 'select',
                                    'options' => $status,
                                    'id' => 'filterStatus',
                                    'class' => 'form-control form-select',
                                    'label' => false,
                                    'empty' => __('Filter By Status'),
                                    'data' => ['bs-toggle' => 'dropdown'],
                                    'aria-expanded' => 'false'
                            ]) ?>
                        </div>
                        <div class="form-group ms-4">
                            <button class="btn btn-primary" id="filter">
                                <i class="fa fa-filter" aria-hidden="true"></i>
                            </button>
                            <button type="reset" class="btn btn-primary" onclick="resetFilters()"><i
                                    class="fas fa-redo-alt"></i></button>
                        </div>
                    </div>
                    <hr />
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped"
                        id="table-1">
                        <thead>
                            <tr>
                                <th><?= __("Id") ?></th>
                                <th><?= __("Category Name") ?></th>
                                <th><?= __("Category Name (Arabic)") ?></th>
                                <th><?= __("Parent Catgory") ?></th>
                                <th><?= __("Country") ?></th>
                                <th id="status"><?= __("Status") ?></th>
                                <?php if ($canView || $canEdit || $canDelete): ?>
                                    <th class="actions"><?= __("Actions") ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $i = 1;

                            function renderCategoryRows($categories, &$i, $Html, $Form, $Url, $statusMap, $canView, $canEdit, $canDelete, $level = 0)
                            {
                                foreach ($categories as $category) {
                                    // Generate prefix based on the level of hierarchy
                                    //$prefix = str_repeat('-- ', $level);
                                    $prefix = str_repeat('', $level);

                                    echo '<tr>';
                                    echo '<td>' . h($i) . '</td>';
                                    echo '<td>' . $prefix . h($category->name) . '</td>';
                                    echo '<td dir="rtl">' . (!empty($category->name_ar) ? h($category->name_ar) : '-') . '</td>';
                                    echo '<td>' . h($category->parent_category ? $category->parent_category->name : 'N/A') . '</td>';
                                    // Country column
                                    echo '<td>' . (isset($category->country) && !empty($category->country->name) ? h($category->country->name) : '-') . '</td>';
                                    ?>
                                    <td>
                                    <?php
                                        $statusval = $statusMap[$category->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <span class="badge-outline <?= $statusval['class'] ?>">
                                            <?= h($statusval['label']) ?>
                                        </span>

                                    </td>

                                    <?php if ($canView || $canEdit || $canDelete):
                                        echo '<td>';
                                        if ($canView):
                                        echo $Html->link(
                                            '<i class="far fa-eye m-r-10"></i>',
                                            ['action' => 'view', $category->id],
                                            ['escape' => false, 'data-toggle' => 'tooltip', 'title' => 'View', 'class' => '']
                                        );
                                        endif;
                                        if ($canEdit):
                                        echo $Html->link(
                                            '<i class="fas fa-pencil-alt m-r-10"></i>',
                                            ['action' => 'edit', $category->id],
                                            ['escape' => false, 'data-toggle' => 'tooltip', 'title' => 'Edit', 'class' => '']
                                        );
                                        endif;
                                        if ($canDelete): ?>
                                        <a href="<?= $Url->build(['controller' => 'Categories', 'action' => 'delete', $category->id]) ?>"
                                            class="delete-btn" data-toggle="tooltip" title="Delete">
                                            <i class="far fa-trash-alt"></i>
                                        </a>
                                    <?php endif;
                                    echo '</td>';
                                    endif;
                                    echo '</tr>';

                                    $i++; // Increment the counter for each row

                                    if (!empty($category->children)) {
                                        // Recursive call with increased level
                                        renderCategoryRows($category->children, $i,  $Html, $Form, $Url, $statusMap, $canView, $canEdit, $canDelete, $level + 1,);
                                    }
                                }
                            }

                            renderCategoryRows($categories, $i, $this->Html, $this->Form, $this->Url, $statusMap, $canView, $canEdit, $canDelete, 0);
                            ?>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 },
            {
                // Make Arabic name column right-aligned
                className: "text-right",
                targets: 2
            }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        drawCallback: function() {
            var api = this.api();
            api.column(0, {search: 'applied', order: 'applied'}).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // table.column(3).search('Active', true, false, false).draw();

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    $("#filter").on("click", function(event) {
        event.preventDefault();
        var status = $("#filterStatus").val();
        var statusMap = <?= json_encode($status) ?>;
        var statusVal = statusMap[status] !== undefined ? statusMap[status] : '';

        if (statusVal == '') {
            table.column(4).search('Active', true, false, false).draw();
        } else {
            table.column(4).search(statusVal, true, false, false).draw();
        }
        table.draw();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        table.column(4).search('Active', true, false, false).draw();
        table.draw();
    }

    $(document).ready(function () {
        var csrfToken = $('meta[name="csrf-token"]').attr('content');

        $(document).on('click', '.delete-btn', function (e) {
            e.preventDefault();

            var url = $(this).attr('href');
            var row = $(this).closest('tr');

            swal({
                title: "<?= __('Are you sure?') ?>",
                text: "<?= __('Once deleted, all it\'s child will be deleted!') ?>",
                icon: "warning",
                buttons: true,
                dangerMode: true,
            }).then((result) => {
                if (result) {
                    $.ajax({
                        url: url,
                        type: 'POST',
                        headers: {
                            'X-CSRF-Token': csrfToken
                        },
                        data: { _method: 'DELETE' },
                        dataType: 'json',
                        success: function (response) {
                            if(response.success){
                                swal(response.message, {
                                    icon: "success",
                                });
                                row.remove();
                            } else {
                                swal(response.message, {
                                    icon: "warning",
                                });
                            }
                        },
                        error: function () {
                            swal("<?= __('Your record is safe!') ?>");
                        }
                    });
                } else {
                    swal("<?= __('Your record is safe!') ?>");
                }
            });
        });
    });
</script>
<?php $this->end(); ?>