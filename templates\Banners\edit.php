<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Banner $banner
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <style>
        input,
        select,
        textarea {
            width: 300px;
            padding: 5px;
            margin-bottom: 10px;
        }

        .is-invalid-select {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        .web-media-container {
            position: relative;
            width: 50%;
        }
    </style>
</head>
<?php $this->end(); ?>

<body>
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Banners', 'action' => 'index']) ?>">
                    <?= __('Banners') ?>
                </a>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Edit') ?>
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
    </div>


    <div class="section-body">
        <div class="container-fluid">
            <div class="card">
                <?php echo $this->Form->create($banner, ['id' => 'editBannersForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
                <h6 class="m-b-20" style="color: #206331">
                    <?= __('Edit Banner') ?>
                </h6>
                <div class="form-group row">
                    <label for="title" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Title') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'title',
                            'placeholder' => __('Enter Banner Title'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label for="title" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Title (Arabic)') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title_ar', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'title_ar',
                            'placeholder' => __('Enter Banner Title (Arabic)'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <label for="web-media" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Image') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('web_media_file', [
                            'type' => 'file',
                            'class' => 'form-control',
                            'id' => 'web-media',
                            'placeholder' => __('Banner Image'),
                            'accept' => 'image/*',
                            'data-max-size' => '10MB',
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <span id="Image-suggestions" class="Image-suggestions"><small>Suggested: <?= implode(', ', $webImageTypedisp) ?> files. Max size: <?= $webImageSize ?> MB.</small></span>
                        <div class="web-media-container">

                        </div>
                        <div class="invalid-feedback">
                        </div>
                        <?= $this->Form->hidden('existing_web_media', [
                            'value' => !empty($banner->web_banner) ? $banner->web_banner : '',
                            'id' => 'existing_web_media'
                        ]); ?>

                        <?php if (!empty($banner->web_banner)) :
                            $fileName = basename($banner->web_banner);
                            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                            $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

                            $shortName = strlen($nameWithoutExtension) > 14 ? substr($nameWithoutExtension, 0, 11) : $nameWithoutExtension;
                            $shortName .= '.' . $extension;
                        ?>
                            <div class="mt-3 web-media-container" style="position: relative; display: inline-block;">
                                <img src="<?= $web_media; ?>" alt="<?= __('Banner Image') ?>" style="width:100%;" />
                                <span class="image-name" title="<?= $fileName; ?>"><?= $shortName; ?></span>
                                <button type="button" class="delete-img-btn web-media" data-id="<?= $banner->id; ?>" data-type="web">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>



                <div class="form-group row">
                    <label for="country" class="col-sm-2 col-form-label fw-bold"><?= __('Country') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <input type="text" class="form-control" id="country-display"
                               value="<?= h($selectedCountryName ?? __('No country assigned')) ?>"
                               readonly style="background-color: #f8f9fa;">
                        <small class="form-text text-muted">
                            <?= __('Country assigned to this banner') ?>
                        </small>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="banner-location" class="col-sm-2 col-form-label fw-bold"><?= __('Banner Location') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">

                        <?php echo $this->Form->control('banner_location', [
                            'type' => 'select',
                            'id' => 'banner-location',
                            'options' => $bannerloc,
                            'class' => 'form-control',
                            'label' => false,
                            'empty' => __('Select a Banner Location'),
                            'value' => $banner->banner_location,
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="banner-description"
                        class="col-sm-2 col-form-label fw-bold"><?= __('Banner Description') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('summary', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'banner-description',
                            'placeholder' => __('Banner Description'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="banner-description_ar"
                        class="col-sm-2 col-form-label fw-bold"><?= __('Banner Description (Arabic)') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('summary_ar', [
                            'type' => 'textarea',
                            'class' => 'form-control',
                            'id' => 'banner-description_ar',
                            'placeholder' => __('Banner Description (Arabic)'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>



                <div class="form-group row">
                    <label for="url-link" class="col-sm-2 col-form-label fw-bold"><?= __('URL Link') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('url_link', [
                            'type' => 'url',
                            'class' => 'form-control',
                            'id' => 'url-link',
                            'placeholder' => __('Enter Url Link'),
                            'label' => false
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>





                <div class="form-group row">
                    <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __('Status') ?><sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('status', [
                            'type' => 'select',
                            'id' => 'status',
                            'options' => $status,
                            'class' => 'form-control select2',
                            'label' => false,
                            'empty' => __('Select a Status'),
                            'required' => true,
                            'value' => $banner->status
                        ]) ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" id="btnSubmit" class="btn"><?= __('Save Banner') ?></button>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
</body>

<?php $this->append('script'); ?>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>

<script>
    $(document).ready(function() {
        $('#web-media').on('change', function() {
            let isValid = true;
            $('.web-media-container').empty();

            // Basic file validation (file type and size only)
            const file = this.files[0];
            if (file) {
                // Check file type using both MIME type and file extension
                const allowedMimeTypes = <?= json_encode($webImageType) ?>;
                const allowedExtensions = <?= json_encode($webImageTypedisp) ?>;
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                const fileMimeType = file.type;

                // Create a more robust MIME type check
                const validMimeTypes = [
                    'image/jpeg', 'image/jpg', 'image/png', 'image/svg+xml', 'image/svg'
                ];

                const isValidExtension = allowedExtensions.includes(fileExtension);
                const isValidMimeType = validMimeTypes.includes(fileMimeType) || allowedMimeTypes.includes(fileMimeType);

                if (!isValidExtension && !isValidMimeType) {
                    alert('<?= __("Invalid file type. Please select a valid image file") ?> (<?= implode(", ", $webImageTypedisp) ?>)');
                    $(this).val('');
                    isValid = false;
                    return;
                }

                // Check file size
                const maxSize = <?= $webImageSize ?> * 1024 * 1024; // Convert MB to bytes
                if (file.size > maxSize) {
                    alert('<?= __("File size exceeds the maximum allowed size of") ?> <?= $webImageSize ?> MB');
                    $(this).val('');
                    isValid = false;
                    return;
                }
            }

            if (isValid && file) {
                renderMediaPreview();
            }
        });

        function renderMediaPreview() {
            $('.web-media-container').empty();
            const webFile = $('#web-media')[0].files[0];

            if (webFile) {
                const webReader = new FileReader();
                const webfileName = $('#web-media')[0].files[0].name;
                webReader.onload = function(e) {
                    $('.web-media-container').html(`<img src="${e.target.result}" alt="Banner Image Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Banner Image Preview">${webfileName}</span>
                    <button type="button" class="delete-img-btn delete-web-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                webReader.readAsDataURL(webFile);
            }
        }

        function validateForm() {
            let isValid = true;

            $('#editBannersForm').find('input[required], select[required], input[type=radio][required]').each(function() {
                if ($(this).attr('type') === 'file') {
                    let fileInput = $(this);
                    let existingFile = '';

                    if (fileInput.attr('id') === 'web-media') {
                        existingFile = $('#existing_web_media').val();
                    }

                    if (!fileInput.val() && !existingFile) {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/WebMobileBoth$/, '');
                        fieldName = fieldName.replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                } else if ($(this).attr('type') === 'radio') {
                    let name = $(this).attr('name');
                    if ($('input[name="' + name + '"]:checked').length === 0) {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                } else {
                    let value = $(this).val().trim();
                    let isSelect2 = $(this).hasClass('select2');
                    if (value === '') {
                        $(this).addClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/WebMobileBoth$/, '');
                        fieldName = fieldName.replace(/\*$/, '');
                        feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                        isValid = false;
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').addClass('is-invalid-select');
                        }
                    } else {
                        $(this).removeClass('is-invalid');
                        let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                        if (isSelect2) {
                            $(this).closest('.form-group').find('.select2-selection--single').removeClass('is-invalid-select');
                        }
                    }
                }
            });

            if (isValid) {
                let url_link = $('#url-link').val().trim();
                if (url_link != '') {
                    let urlRegex = /^(https?:\/\/)?([\w\d\-_]+\.)+[\w\d\-_]+(\/[\w\d\-_]+)*\/?$/i;
                    if (!urlRegex.test(url_link)) {
                        $('#url-link').addClass('is-invalid');
                        let feedback = $('#url-link').closest('.form-group').find('.invalid-feedback');
                        feedback.text('<?= __('URL is not in the correct format') ?>.').show();
                        isValid = false;
                    } else {
                        $('#url-link').removeClass('is-invalid');
                        let feedback = $('#url-link').closest('.form-group').find('.invalid-feedback');
                        feedback.hide();
                    }
                }
            }
            return isValid;
        }

        $('#btnSubmit').click(function(event) {
            event.preventDefault();
            if (!validateForm()) {
                return;
            }
            var form = $('#editBannersForm')[0];
            form.action = "<?= $this->Url->build(['controller' => 'Banners', 'action' => 'edit', $banner->id]) ?>";
            $('#btnSubmit').attr('disabled', true);
            form.submit();
        });
    });

    $('.delete-img-btn').on('click', function() {
        var $button = $(this);
        var imageId = $button.data('id');
        var imageType = $button.data('type');

        $.ajax({
            url: "<?= $this->Url->build(['controller' => 'Banners', 'action' => 'deleteImage']) ?>",
            type: 'POST',
            dataType: 'json',
            data: {
                image_type: imageType,
                image_id: imageId
            },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                if (response.status === 'success') {
                    if (imageType === 'web') {
                        $('#existing_web_media').val('');
                        $('.web-media-container').remove();
                    }

                    swal('Success', response.message, 'success');
                } else {
                    swal('Failed', response.message, 'error');
                }
            },
            error: function(xhr) {
                swal('Failed', 'Failed to delete image. Please try again.', 'error');
            }
        });
    });

    $('#web-media').on('click', function(e) {
        var existingMedia = $('#existing_web_media').val();
        if (existingMedia !== '' && existingMedia !== undefined) {
            e.preventDefault();
            swal({
                title: 'Existing Media Found',
                text: 'You already have an existing meida. Please delete the current media before uploading a new one.',
                icon: 'warning',
                buttons: {
                    cancel: 'Cancel',
                    confirm: 'Delete Media and Upload New'
                },
            }).then((willDelete) => {
                if (willDelete) {
                    $('.web-media').trigger('click');
                    $('#existing_web_media').val('');
                    $('#web-media').trigger('click');
                }
            });
        }
    });

    $(document).on('click', '.delete-web-media', function(event) {
        $('.web-media-container').empty();
        $('#web-media').val('');
    });
</script>
<?php $this->end(); ?>