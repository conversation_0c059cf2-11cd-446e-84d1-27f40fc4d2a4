<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\Zone> $zones
 */
?>

<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style type="text/css">
    .reset_cms_filter{
        margin-right: 0px !important;
    }
</style>
<?php $this->end(); ?>

<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item active"><?= __('CMS Pages') ?></li>
    </ul>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><?= __('Manage CMS Pages') ?></h4>
                <div class="card-header-form d-flex align-items-center">
                    <form class="d-flex align-items-center m-l-10">
                        <div class="input-group">
                            <input type="text" class="form-control search-control" placeholder="Search" id="customSearchBox" />
                            <div class="input-group-btn">
                                <button class="btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <?php if ($canAdd): ?>
                                <a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'add']) ?>"
                                    style="position: relative;left: 10px;"
                                    class="btn me-2">
                                    <i class="fas fa-plus"></i>
                                    <?= __('Add CMS Page') ?>
                                </a>
                            <?php endif; ?>
                            <button class="btn menu-toggle fw-bold" style="position: relative;left: 26px;" type="submit">
                                <i class="fas fa-filter"></i>
                                <?= __('Filter') ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div id="filter-body-container">
                <div class="" style="margin-top: 0px !important;margin-right: 0px !important;">
                    <form method="get" accept-charset="utf-8" class="form-inline filter-rating attribute"
                        id="filter-search" action="">
                        <div class="d-flex card-body" style="background-color: rgba(13, 131, 155, -0.096) !important;">
                            <div class="d-flex align-items-center">
                                <?php 
                                $content_categories = array(); 
                                foreach ($content_pages as $content): 
                                    
                                    array_push($content_categories, $content->content_category);
                                
                                endforeach; 

                                $categoryData = array_unique($content_categories);

                                ?>
                                <label class="m-r-5" for="category" style="width: 160px;"><?= __('Content Category:') ?></label>
                                <select name="category" id="filterCategory" class="form-select">
                                    <option value=""><?= __("Filter By Content Category") ?></option>
                                    <?php foreach ($categoryData as $value): ?>
                                        <option value="<?= h($value) ?>"><?= h($value) ?></option> 
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="d-flex align-items-center m-l-20">
                                <label class="m-r-5" for="published" style="width: max-content;"><?= __('Published:') ?></label>
                                <select name="published" id="filterPublished" class="form-select">
                                    <option value=""><?= __("Filter By Published") ?></option>
                                    <option value="Yes"><?= __("Yes") ?>
                                    </option>
                                    <option value="No"><?= __("No") ?>
                                    </option>
                                </select>
                            </div>
                            <div class="d-flex align-items-center m-l-20"
                            >
                                <label class="m-r-5" for="status" style="width: max-content;"><?= __('Status:') ?></label>
                                <select name="status" id="filterStatus" class="form-select">
                                    <option value=""><?= __('Filter By Status') ?></option>
                                    <option value="A"><?= __('Active') ?></option>
                                    <option value="I"><?= __('Inactive') ?></option>
                                </select>
                            </div>
                            <div class="d-flex">
                                <button type="button" style="margin-right: 0px !important;" class="m-l-20 btn cms_filter">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                                <button type="reset" class="m-l-10 btn reset_cms_filter"><i class="fas fa-redo-alt"></i></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="cmsTable" role="grid">
                        <thead>
                            <tr>
                                <th><?= __('Content Category') ?></th>
                                <th><?= __('Title') ?></th>
                                <th><?= __('Title (Arabic)') ?></th>
                                <th><?= __('Description') ?></th>
                                <th><?= __('Description (Arabic)') ?></th>
                                <th><?= __('Content Tag') ?></th>
                                <th><?= __('Country') ?></th>
                                <th id="status"><?= __('Status') ?></th>
                                <th id="published"><?= __('Published') ?></th>
                                <th class="actions"><?= __('Actions') ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($content_pages as $content): ?>
                            <tr>
                                <td><?php echo !empty($content->content_category) ? h($content->content_category) : 'N/A'; ?></td>
                                <td><?php echo !empty($content->title) ? h($content->title) : 'N/A'; ?></td>
                                <td><?php echo !empty($content->title_ar) ? h($content->title_ar) : 'N/A'; ?></td>
                                <td><?php echo !empty($content->description) ? h($content->description) : 'N/A'; ?></td>
                                <td><?php echo !empty($content->description_ar) ? h($content->description_ar) : 'N/A'; ?></td>
                                <td><?php echo !empty($content->tag) ? h($content->tag) : 'N/A'; ?></td>
                                <td><?php echo isset($content->country) && !empty($content->country->name) ? h($content->country->name) : '-'; ?></td>
                                <td>
                                    <?php
                                        $statusMap = [
                                            'A' => ['label' => 'Active', 'class' => 'col-green'],
                                            'I' => ['label' => 'Inactive', 'class' => 'col-red'],
                                            'D' => ['label' => 'Deleted', 'class' => 'col-red']
                                        ];

                                        $status = $statusMap[$content->status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                        ?>
                                        <div class="badge-outline <?= $status['class'] ?>">
                                            <?= h($status['label']) ?>
                                        </div>
                                </td>
                                <td><?= h($content->published) ?></td>
                                <td class="actions">
                                    <?php if ($canView): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'view', $content->id]) ?>"
                                            class="" data-toggle="tooltip" title="View"><i class="far fa-eye m-r-10"></i></a>
                                    <?php endif; ?>
                                    <?php if ($canEdit): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'edit', $content->id]) ?>"
                                            class="" data-toggle="tooltip" title="Edit"><i
                                                class="fas fa-pencil-alt m-r-10"></i></a>
                                    <?php endif; ?>
                                    <?php if ($canDelete): ?>
                                        <a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'delete', $content->id]) ?>"
                                            class="delete-btn" data-toggle="tooltip" title="Delete"
                                            data-delete-confirmation="<?= addslashes($deleteConfirmationMessage); ?>"
                                            data-delete-warning="<?= addslashes($deleteWarningMessage); ?>"
                                            data-delete-fail="<?= addslashes($deleteFailMessage); ?>">
                                                <i class="far fa-trash-alt"></i>
                                        </a>
                                    <?php endif; ?>    
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>

    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#cmsTable").DataTable({
        order: [],
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'rtip',
        pageLength: paginationCount
    });

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    $('.cms_filter').on('click', function () {

        var content_category = $("#filterCategory option:selected").val();
        var published = $("#filterPublished option:selected").val();
        var status = $("#filterStatus option:selected").val();
        var filterStatusValue = status === 'A' ? 'Active' : status === 'I' ? 'Inactive' : '';

        table.search('').columns().search('');

        if (content_category) {
            table.column(0).search(content_category).draw();
        }

        if (published) {
            table.column(5).search(published).draw();
        }

        if (status) {
            table.column(4).search(filterStatusValue, true, false, false).draw();
        }
    });

    $('.reset_cms_filter').on('click', function () {

        table.search('').columns().search('').draw();

    });

</script>
<?php $this->end(); ?>
