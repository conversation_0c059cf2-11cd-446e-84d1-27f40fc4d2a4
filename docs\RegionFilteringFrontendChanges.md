# Region-Based Filtering Frontend Template Changes

This document outlines the frontend template changes needed to display country information and enforce country selection in the admin panel for Brand, Category, Banners, CMS pages, and Widgets modules.

## Overview

The country filtering system is already implemented in the header (`templates/element/header.php`) with a dropdown that allows users to filter by country. The backend controllers now apply region-based filtering, and the frontend templates need to be updated to:

1. **Display country information** in index/listing pages
2. **Show country assignment** in add/edit forms
3. **Enforce country selection** before allowing record creation
4. **Prevent form submission** without country selection

## Template Changes Required

### 1. Categories Templates

#### A. Categories Index Template (`templates/Categories/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($category->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($category->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

#### B. Categories Add Template (`templates/Categories/add.php`)

**Add Country Assignment Section (after description fields):**
```php
<!-- Country Assignment Info (Read-only display) -->
<?php
$currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId');
if ($currentCountryFilter && isset($selectedCountry)): ?>
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-info mb-0">
            <i class="fas fa-info-circle me-2"></i>
            <?= __('This category will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
            <br>
            <small class="text-muted">
                <?= __('To change country, use the country dropdown in the header.') ?>
            </small>
        </div>
    </div>
</div>
<?php elseif (!$currentCountryFilter): ?>
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-warning mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= __('Please select a country from the header dropdown before adding a category.') ?>
            <br>
            <small class="text-muted">
                <?= __('Categories must be assigned to a specific country.') ?>
            </small>
        </div>
    </div>
</div>
<?php endif; ?>
```

**Add JavaScript Validation (in the script section):**
```javascript
$(document).ready(function () {
    // Check country selection before form submission
    <?php $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId'); ?>
    <?php if (!$currentCountryFilter): ?>
    // If no country is selected, disable form submission
    $('#category-form').on('submit', function(e) {
        e.preventDefault();
        swal({
            title: '<?= __("Country Required") ?>',
            text: '<?= __("Please select a country from the header dropdown before adding a category. Categories must be assigned to a specific country.") ?>',
            icon: 'warning',
            button: '<?= __("OK") ?>'
        });
        return false;
    });
    <?php endif; ?>

    // ... rest of existing validation code
});
```

#### C. Categories Edit Template (`templates/Categories/edit.php`)

**Add Country Assignment Display (after description fields):**
```php
<!-- Country Assignment Display -->
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-info mb-0">
            <i class="fas fa-info-circle me-2"></i>
            <?php if (!empty($category->country)): ?>
                <?= __('This category is assigned to: <strong>{0}</strong>', h($category->country->name)) ?>
            <?php else: ?>
                <?= __('This category is assigned to: <strong>Global (All Countries)</strong>') ?>
            <?php endif; ?>
            <br>
            <small class="text-muted">
                <?= __('Country assignment was set when the category was created.') ?>
            </small>
        </div>
    </div>
</div>
```

### 2. Banners Templates

#### A. Banners Index Template (`templates/Banners/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($banner->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($banner->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

#### B. Banners Add Template (`templates/Banners/add.php`)

**Add Country Assignment Section (after title/summary fields):**
```php
<!-- Country Assignment Info (Read-only display) -->
<?php
$currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId');
if ($currentCountryFilter && isset($selectedCountry)): ?>
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-info mb-0">
            <i class="fas fa-info-circle me-2"></i>
            <?= __('This banner will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
            <br>
            <small class="text-muted">
                <?= __('To change country, use the country dropdown in the header.') ?>
            </small>
        </div>
    </div>
</div>
<?php elseif (!$currentCountryFilter): ?>
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-warning mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= __('Please select a country from the header dropdown before adding a banner.') ?>
            <br>
            <small class="text-muted">
                <?= __('Banners must be assigned to a specific country.') ?>
            </small>
        </div>
    </div>
</div>
<?php endif; ?>
```

#### C. Banners Edit Template (`templates/Banners/edit.php`)

**Add Country Assignment Display:**
```php
<!-- Country Assignment Display -->
<div class="form-group row">
    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
    <div class="col-sm-5">
        <div class="alert alert-info mb-0">
            <i class="fas fa-info-circle me-2"></i>
            <?php if (!empty($banner->country)): ?>
                <?= __('This banner is assigned to: <strong>{0}</strong>', h($banner->country->name)) ?>
            <?php else: ?>
                <?= __('This banner is assigned to: <strong>Global (All Countries)</strong>') ?>
            <?php endif; ?>
            <br>
            <small class="text-muted">
                <?= __('Country assignment was set when the banner was created.') ?>
            </small>
        </div>
    </div>
</div>
```

### 3. Content Pages Index Template (`templates/ContentPages/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($content_page->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($content_page->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

### 4. Widgets Index Template (`templates/Widgets/index.php`)

**Add Country Column Header:**
```php
// In the table header section, add:
<th><?= __("Country") ?></th>
```

**Add Country Display in Table Body:**
```php
// In the table body foreach loop, add:
<td>
    <?php if (!empty($widget->country)): ?>
        <span class="badge badge-success">
            <i class="fas fa-map-marker-alt me-1"></i><?= h($widget->country->name) ?>
        </span>
    <?php else: ?>
        <span class="badge badge-secondary">
            <i class="fas fa-globe me-1"></i><?= __('Global') ?>
        </span>
    <?php endif; ?>
</td>
```

## Country Filter Header

The country filter dropdown is already implemented in `templates/element/header.php` and includes:

- **All Countries** option (shows all records regardless of country)
- **Individual Country** options (filters records by specific country)
- **Visual indicators** with icons and badges
- **Session persistence** (selection is saved and maintained across pages)

## CSS Classes Used

- `badge badge-success` - Green badge for specific countries
- `badge badge-secondary` - Gray badge for global/no country assigned
- `fas fa-map-marker-alt` - Icon for specific countries
- `fas fa-globe` - Icon for global/all countries

## JavaScript Integration

The country filtering works automatically through the existing JavaScript in the header that:

1. Handles dropdown selection
2. Sends AJAX requests to update session
3. Refreshes the page to apply filters
4. Updates the dropdown display text

## Controller Updates Required

All controllers need to pass the selected country data to their add templates. Add this code to each controller's `add()` method:

```php
// Get selected country for display
$selectedCountryId = $this->request->getSession()->read('Admin.selectedCountryId');
$selectedCountry = null;
if ($selectedCountryId) {
    $selectedCountry = $this->Countries->getCountryById($selectedCountryId);
}

// Add 'selectedCountry' to the compact() call
$this->set(compact('...existing_variables...', 'selectedCountry'));
```

**Controllers to update:**
- `src/Controller/BrandsController.php` ✅ (Already updated)
- `src/Controller/CategoriesController.php` ✅ (Already updated)
- `src/Controller/BannersController.php` (Needs update)
- `src/Controller/ContentPagesController.php` (Needs update)
- `src/Controller/WidgetsController.php` (Needs update)

## Implementation Notes

1. **Country Association**: All models now have `belongsTo('Countries')` relationship
2. **Controller Filtering**: All controllers use `applyRoleBasedCountryFilter()` method
3. **Permission Checks**: Edit/delete actions check `canUserAccessCountry()` before allowing access
4. **Super Admin Protection**: Super admin roles and users cannot be deleted/modified
5. **Database Migration**: Run the migration to add `country_id` fields to all tables
6. **Country Selection Enforcement**: Forms prevent submission without country selection
7. **Visual Indicators**: Clear display of country assignment in forms and listings

## Next Steps

1. Run the database migration: `bin/cake migrations migrate`
2. Update the frontend templates as shown above
3. Test the filtering functionality with different user roles
4. Verify permission restrictions work correctly
