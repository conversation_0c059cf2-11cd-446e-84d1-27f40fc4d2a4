<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        #toast-message-container .alert {
            transition: none !important;
            opacity: 1 !important;
        }
    </style>
    <style>
span.show-password-icon-register {
    position: absolute;
    top: 5%;
    right: 4%;
    color:black;
    z-index: 3;
}
</style>
<!-- Toast Message Container -->
<div id="toast-message-container" style="position: fixed; top: 80px; right: 20px; z-index: 9999; max-width: 350px;"></div>

<div class="row login-admin-form">
    <div class="col-md-6"></div>
    <div class="col-md-6">
        
        <a class="navbar-brand login-form" href="#">
            <img class="m-auto img-fluid" src="<?= $this->Url->webroot('img/ozone/Ozonex_Logo_4.png') ?>" />
        </a>
        <div class="store-card">
            <h4>Reset Password</h4>
            <?= $this->Form->create(null, ['url' => ['controller' => 'Customer', 'action' => 'resetPassword', $token]]) ?>
            <div class="input-border input-group">
                <input type="password" name="password" id="password" class="form-control rounded-pill" placeholder="Enter Password" required/>
                <span class="show-password-icon-register" onclick="togglePasswordVisibilityRegister(this)"><i class="fa fa-eye"></i></span>
                <div class="border"></div>
            </div>
            <div class="input-border input-group">
                <input type="password" name="confirm_password" id="confirm_password" class="form-control rounded-pill" placeholder="Confirm Password" required/>
                <span class="show-password-icon-register" onclick="togglePasswordVisibilityRegister(this)"><i class="fa fa-eye"></i></span>
                <div class="border"></div>
            </div>
            <div class="text-center" id="passwordError" style="margin-bottom:10px;color:orange;display:none;font-size:16px"></div>
            <div class="text-center password_error" style="margin-bottom:10px;color:orange;display:none;font-size:16px">Confirm Password must be same as Password</div>
            <input type="submit" class="btn" value="Reset Password" />
            </form>
        </div>
    </div>
</div>

<script>
    $('#password').on('keyup', function () {
            var password = $(this).val();
            var errorMsg = '';

            if (password.length < 8) {
                $('#passwordError').text('Password must be at least 8 characters long.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[A-Z]/.test(password)) {
                $('#passwordError').text('Password must include at least one uppercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[a-z]/.test(password)) {
                $('#passwordError').text('Password must include at least one lowercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[0-9]/.test(password)) {
                $('#passwordError').text('Password must include at least one number.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                $('#passwordError').text('Password must include at least one special character.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            }
            else{
                $('#passwordError').text('').hide()
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });

    //this will check password and confirm password match on keyup event
    $('#confirm_password').on('keyup', function() {
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        if (password !== confirmPassword) {
            $('.password_error').show();
            $('.btn-login').prop('disabled', true);
        } else {
            $('.password_error').hide();
            $('.btn-login').prop('disabled', false);
        }
    });

    function togglePasswordVisibilityRegister(icon) {
        const input = icon.previousElementSibling;
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);

        // Optionally toggle the eye icon to eye-slash
        const iconTag = icon.querySelector('i');
        if (iconTag) {
            iconTag.classList.toggle('fa-eye');
            iconTag.classList.toggle('fa-eye-slash');
        }
    }

    function toggleIconVisibilityRegister(input) {
        const icon = input.nextElementSibling;
        if (input.value.length > 0) {
            icon.style.display = 'inline';
        } else {
            icon.style.display = 'none';
        }
    }
</script>
<?php $session = $this->request->getSession(); ?>
<script>
    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }
</script>