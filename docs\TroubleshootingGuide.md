# Region-Based Filtering Troubleshooting Guide

## Issues and Solutions

### 1. Error: "Call to undefined method handleCountryAssignment()"

**Status**: ✅ **FIXED**

**Issue**: The method `handleCountryAssignment()` was referenced but didn't exist.

**Solution**: Replaced with proper country assignment logic:
```php
// Handle country_id assignment based on current country filter
$selectedCountryId = $this->getCurrentCountryFilter();
if ($selectedCountryId) {
    // Use the country selected in header dropdown
    $data['country_id'] = $selectedCountryId;
} else {
    // If "All Countries" is selected, force user to select a specific country first
    $this->Flash->error(__('Please select a specific country from the header dropdown before adding a [item]. [Items] must be assigned to a specific country.'));
    
    // Redirect back to add form
    return $this->redirect(['action' => 'add']);
}
```

### 2. Issue: "Global" showing in index/listing pages

**Possible Causes**:
1. **Database migration not run** - `country_id` fields don't exist
2. **Existing records have NULL country_id** - Records created before migration
3. **Association not loading properly** - Countries relationship not working

**Solutions**:

#### A. Run Database Migration
```bash
# Navigate to your CakePHP app directory
cd c:\wamp64\www\carmatec\Ozone\cakephpapp

# Run the migration
bin/cake migrations migrate
```

#### B. Check Database Schema
Verify these tables have `country_id` field:
- `brands`
- `categories` 
- `banners`
- `content_pages`
- `widgets`

#### C. Update Existing Records
If you have existing records with NULL country_id, you can update them:
```sql
-- Example: Set all NULL country_id to a default country (e.g., UAE = 1)
UPDATE brands SET country_id = 1 WHERE country_id IS NULL;
UPDATE categories SET country_id = 1 WHERE country_id IS NULL;
UPDATE banners SET country_id = 1 WHERE country_id IS NULL;
UPDATE content_pages SET country_id = 1 WHERE country_id IS NULL;
UPDATE widgets SET country_id = 1 WHERE country_id IS NULL;
```

### 3. Issue: Country selection not enforced in add forms

**Status**: ✅ **FIXED**

**Solution**: Added proper validation in controllers that:
1. Checks if country is selected in header dropdown
2. Assigns country_id to new records
3. Shows error and redirects if no country selected

### 4. Issue: Permission errors on edit/delete

**Possible Cause**: User trying to access records from non-accessible countries

**Solution**: The system now checks permissions:
```php
// Check if user can access this record's country
if (!$this->canUserAccessCountry($record->country_id)) {
    $this->Flash->error(__('You do not have permission to access this [item].'));
    return $this->redirect(['action' => 'index']);
}
```

## Testing Steps

### 1. Verify Database Migration
```bash
# Check if migration was successful
bin/cake migrations status
```

### 2. Test Country Assignment
1. Go to header dropdown and select a specific country
2. Try to add a new brand/category/banner/content page/widget
3. Should show country assignment info
4. Should save with correct country_id

### 3. Test Country Filtering
1. Select different countries in header dropdown
2. Check if index pages show only records from selected country
3. Try "All Countries" option

### 4. Test Permission Restrictions
1. Login with user having limited country access
2. Try to edit/delete records from non-accessible countries
3. Should get permission error

## Debug Commands

### Check Database Structure
```sql
-- Check if country_id field exists
DESCRIBE brands;
DESCRIBE categories;
DESCRIBE banners;
DESCRIBE content_pages;
DESCRIBE widgets;
```

### Check Existing Data
```sql
-- Check country_id values in tables
SELECT id, name, country_id FROM brands LIMIT 10;
SELECT id, name, country_id FROM categories LIMIT 10;
SELECT id, title, country_id FROM banners LIMIT 10;
SELECT id, title, country_id FROM content_pages LIMIT 10;
SELECT id, title, country_id FROM widgets LIMIT 10;
```

### Check Countries Table
```sql
-- Verify countries exist
SELECT id, name FROM countries ORDER BY name;
```

## Current Implementation Status

### ✅ Backend (Complete)
- [x] Database migration created
- [x] Model associations added
- [x] Controllers updated with filtering logic
- [x] Country assignment logic implemented
- [x] Permission checks added
- [x] Super admin protection added

### ⏳ Frontend (Partial)
- [x] Brands index template updated
- [x] Brands add/edit templates updated
- [ ] Other module templates need updates

## Next Steps

1. **Run Migration**: `bin/cake migrations migrate`
2. **Test Brand Module**: Verify country assignment and filtering works
3. **Update Other Templates**: Use examples in `RegionFilteringFrontendChanges.md`
4. **Test All Modules**: Follow test plan in `RegionFilteringTestPlan.md`

## Common Error Messages

- **"Call to undefined method handleCountryAssignment()"** → Fixed in controllers
- **"Please select a specific country..."** → User needs to select country in header
- **"You do not have permission to access..."** → User lacks country access rights
- **"Global" showing in listings** → Migration not run or NULL country_id values

## Support

If issues persist:
1. Check error logs in `logs/error.log`
2. Verify database schema matches migration
3. Test with different user roles and country assignments
4. Clear cache: `bin/cake cache clear_all`
