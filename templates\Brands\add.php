<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Brand $brand
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Catalogue") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'index']) ?>"><?= __("Brands") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Add") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Add Brand") ?></h6>
            <?php echo $this->Form->create($brand, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
            <div class="form-group row">
                <label for="name" class="col-sm-2 col-form-label fw-bold"><?= __("Brand Name") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name',
                        'placeholder' => __('Brand Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="name_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Brand Name (Arabic)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('name_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'name_ar',
                        'placeholder' => __('Brand Name in Arabic'),
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" class="col-sm-2 col-form-label fw-bold"><?= __("Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description',
                        'placeholder' => __('Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Description (Arabic)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description_ar',
                        'placeholder' => __('Description in Arabic'),
                        'label' => false,
                        'dir' => 'rtl'
                    ]); ?>
                </div>
            </div>

            <!-- Country Assignment Info (Read-only display) -->
            <?php
            $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId');
            if ($currentCountryFilter && isset($selectedCountry)): ?>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                <div class="col-sm-5">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        <?= __('This brand will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
                        <br>
                        <small class="text-muted">
                            <?= __('To change country, use the country dropdown in the header.') ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php elseif (!$currentCountryFilter): ?>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
                <div class="col-sm-5">
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= __('Please select a country from the header dropdown before adding a brand.') ?>
                        <br>
                        <small class="text-muted">
                            <?= __('Brands must be assigned to a specific country.') ?>
                        </small>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="form-group row">
                <label for="brand_logo" class="col-sm-2 col-form-label fw-bold"><?= __("Brand Logo") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('brand_logo', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'brand_logo',
                        'placeholder' => __('Brand Logo'),
                        'label' => false,
                        'accept' => implode(',', $logoType)
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $brandLogoType)) ?> files are allowed. Max size: <?php echo $logoSize; ?> MB. Dimensions: <?= $logoMinWidth ?> x <?= $logoMinHeight ?> and <?= $logoMaxWidth ?> x <?= $logoMaxHeight ?>.</small>
                    <div id="logoPreviewContainer"></div>
                </div>
            </div>
            <div class="form-group row">
                <label for="web_banner" class="col-sm-2 col-form-label fw-bold"><?= __("Brand Banner") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('web_banner', [
                        'type' => 'file',
                        'class' => 'form-control',
                        'id' => 'web_banner',
                        'placeholder' => __('Brand Banner'),
                        'label' => false,
                        'accept' => implode(',', $bannerType)
                    ]); ?>
                    <small>Only <?= implode(', ', array_map(fn($type) => '.' . $type, $brandBannerType)) ?> files are allowed. Max size: <?php echo $bannerSize; ?> MB. Dimensions: <?= $bannerMinWidth ?> x <?= $bannerMinHeight ?> and <?= $bannerMaxWidth ?> x <?= $bannerMaxHeight ?>.</small>
                    <div id="bannerPreviewContainer"></div>
                </div>
            </div>
            <div class="form-group row">
                <label for="parent_id" class="col-sm-2 col-form-label fw-bold"><?= __("Category") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control(
                        'category_id',
                        [
                            'id' => 'category_id_model',
                            'type' => 'select',
                            'label' => false,
                            'div' => false,
                            'title' => __('Select Category'),
                            'options' => $categories,
                            'multiple' => 'multiple',
                            'data-live-search' => "true",
                            'class' => 'form-control select2'
                        ]
                    );
                    ?>
                </div>
            </div>

            <h6 class="m-b-20" style="color: #206331"><?= __("SEO Configuration") ?></h6>
            <div class="form-group row">
                <label for="meta_title" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Title") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_title',
                        'placeholder' => __('Meta Title'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="meta_keyword" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Keyword") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_keyword', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Meta Keyword'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="meta_description"
                    class="col-sm-2 col-form-label fw-bold"><?= __("Meta Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_description',
                        'placeholder' => __('Meta Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('status', [
                        'type' => 'select',
                        'class' => 'form-control form-select',
                        'id' => 'status',
                        'options' => [
                            'A' => __('Active'),
                            'I' => __('Inactive')
                        ],
                        'empty' => __('Select Status'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn btn-primary" id="saveBtn"<?= !$currentCountryFilter ? ' disabled' : '' ?>><?= __("Save") ?></button>
                    <button type="reset" class="btn btn-primary"><?= __("Reset") ?></button>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>
<script>
    $(document).ready(function () {
        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });

        // Disable save button if no country is selected
        <?php if (!$currentCountryFilter): ?>
            $('#saveBtn').prop('disabled', true);
        <?php endif; ?>
    });

    $('#web_banner').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $bannerSize ?> * 1024 * 1024,
            <?= json_encode($bannerType) ?>,
            <?= $bannerMinWidth ?>,
            <?= $bannerMaxWidth ?>,
            <?= $bannerMinHeight ?>,
            <?= $bannerMaxHeight ?>
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 banner-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Web Banner" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#bannerPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        }
    });
    $('#brand_logo').on('change', async function() {

        let isValid = await asyncvalidateFile(
            this,
            <?= $logoSize ?> * 1024 * 1024,
            <?= json_encode($logoType) ?>
            // Dimensions validation removed for brand logo
        );

        if (isValid && this.files && this.files[0]) {
            let file = this.files[0];
            let reader = new FileReader();

            reader.onload = function(e) {
                let base64Url = e.target.result;

                let imageHtml = `
                    <div class="mt-3 logo-container" style="position: relative; display: inline-block; max-width: 100px; max-height: 100px;">
                        <img src="${base64Url}" alt="Brand Logo" style="max-width: 100px; max-height: 100px;">
                        <span class="image-name" title="${file.name}">${file.name}</span>
                    </div>
                `;

                $('#logoPreviewContainer').html(imageHtml);
            };

            reader.onerror = function(error) {
                console.error('Error reading file:', error);
            };

            reader.readAsDataURL(file);
        } else {
            // Clear preview if not valid
            $('#logoPreviewContainer').html('');
            $(this).val('');
        }
    });

    var validationMessages = {
        nameRequired: "<?= __('Please enter brand name') ?>",
     /*   brandLogoRequired: "<?= __('Please add brand logo') ?>", */
        categoryRequired: "<?= __('Please select category') ?>",
        patternError: "<?= __('Only lowercase letters and underscores are allowed') ?>"
    };

    $(document).ready(function () {
        // Check country selection before form submission
        <?php $currentCountryFilter = $this->request->getSession()->read('Admin.selectedCountryId'); ?>
        <?php if (!$currentCountryFilter): ?>
        // If no country is selected, disable form submission
        $('#add').on('submit', function(e) {
            e.preventDefault();
            swal({
                title: '<?= __("Country Required") ?>',
                text: '<?= __("Please select a country from the header dropdown before adding a brand. Brands must be assigned to a specific country.") ?>',
                icon: 'warning',
                button: '<?= __("OK") ?>'
            });
            return false;
        });
        <?php endif; ?>

        $.validator.addMethod("pattern", function (value, element) {
            return this.optional(element) || /^[a-z-]+$/.test(value);
        }, validationMessages.patternError);

        $("#add").validate({
            ignore: "",
            rules: {
                'name': {
                    required: true
                },
                // 'brand_logo': {
                //     required: true
                // },
                'category_id[]': {
                    required: true
                }
            },
            messages: {
                'name': {
                    required: validationMessages.nameRequired
                },
                // 'brand_logo': {
                //     required: validationMessages.brandLogoRequired
                // },
                'category_id[]': {
                    required: validationMessages.categoryRequired
                }
            },
            submitHandler: function (form) {
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');
            }
        });
    });
</script>
<?php $this->end(); ?>