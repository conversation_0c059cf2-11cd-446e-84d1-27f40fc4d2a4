<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Brand $brand
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style type="text/css">
    .error {
        color: red !important;
    }
</style>
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
        </li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'index']) ?>"><?= __("CMS Pages") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Add") ?></li>
    </ul>
    <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
        <small class="p-10 fw-bold"><?= __('BACK') ?></small>
        <span class="rotate me-2">⤣</span>
    </button>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0" style="color: #206331"><?= __("Add CMS Page") ?></h6>
                    </div>
                    <div class="card-body">
                        <?php echo $this->Form->create($content, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
                        <?php if (!empty($content->getErrors())): ?>
                            <div class="alert alert-danger">
                                <div class="validation-errors">
                                    <?php foreach ($content->getErrors() as $field => $errors): ?>
                                        <div class="field-errors mb-2">
                                            <strong><?php echo h(ucwords($field)); ?>:</strong>
                                            <ul class="mb-0">
                                                <?php foreach ($errors as $error): ?>
                                                    <li><?php echo h($error); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($selectedCountry)): ?>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                            <div class="col-sm-5">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?= __('This CMS page will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
                                    <br>
                                    <small class="text-muted">
                                        <?= __('To change country, use the country dropdown in the header.') ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php elseif (empty($selectedCountry)): ?>
                        <div class="form-group row">
                            <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
                            <div class="col-sm-5">
                                <div class="alert alert-warning mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?= __('Please select a country from the header dropdown before adding a CMS page.') ?>
                                    <br>
                                    <small class="text-muted">
                                        <?= __('CMS pages must be assigned to a specific country.') ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

            <div class="form-group row">
                <label for="content_category" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Content Category") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control(
                        'content_category',
                        [
                            'id' => 'content_category',
                            'type' => 'select',
                            'empty' => 'Select Content Category',
                            'label' => false,
                            'div' => false,
                            'title' => __('Select Content Category'),
                            'options' => $menus,
                            'data-live-search' => "true",
                            'class' => 'form-control select2'
                            // 'onChange' => 'handleCategoryIdentifier(this.value)'
                        ]
                    );
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="content_category_identifier" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Content category ID") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('content_category_identifier', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'content_category_identifier',
                        // 'placeholder' => __('Brand Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="title" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Title") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'title',
                        'placeholder' => __('Title'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="title_ar" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Title (Arabic)") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('title_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'title_ar',
                        'placeholder' => __('Title (Arabic)'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="tag" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("CMS Tag Name") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('tag', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'tag',
                        'placeholder' => __('CMS Tag Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description',
                        'placeholder' => __('Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="description_ar" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Description (Arabic)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('description_ar', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'description_ar',
                        'placeholder' => __('Description (Arabic)'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="content_image" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Image(s)") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('content_image[]', ['type' => 'file', 'class' => 'form-control', 'label' => false, 'accept' => 'image/*' ,'multiple' => 'multiple', 'id' => 'imageInput']); ?>
                    <span><?= $file_acceptance_msg ?></span>
                    <div id="previeContainer">
                        <ul id="imagePreviewContainer">

                        </ul>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="image_alt" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Image AltTag") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('image_alt', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'image_alt',
                        'placeholder' => __('Image AltTag'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="video_url" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Video URL") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('video_url', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'video_url',
                        'placeholder' => __('URL'),
                        'label' => false
                    ]); ?>
                </div>
            </div>


           <div class="form-group row">
                <label for="image_alt" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Published") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 d-flex main-field">
                    <div class="form-check mr-3">
                        <input class="form-check-input" style="width: 10px;height: 10px;" type="radio" id="yes" name="published" value="Yes" checked>
                        <label class="form-check-label" for="yes">
                            Yes
                        </label>
                    </div>
                    <div class="form-check mr-3">
                        <input class="form-check-input" style="width: 10px;height: 10px;" type="radio" id="no" name="published" value="No">
                        <label class="form-check-label" for="no">
                            No
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label for="published_date" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Published Date") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('published_date', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'id' => 'published_date',
                        'placeholder' => __('Published Date'),
                        'min' => date('Y-m-d'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="content" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Content") ?> <sup class="text-danger font-11">*</sup></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('content', ['class' => 'form-control', 'id' => 'ckeditor', 'label' => false]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="content_ar" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Content (Arabic)") ?></label>
                <div class="col-sm-10 main-field">
                    <?php echo $this->Form->control('content_ar', ['class' => 'form-control', 'id' => 'ckeditor_ar', 'label' => false]); ?>
                </div>
            </div>

            <h6 class="m-b-20" style="color: #206331"><?= __("SEO Configuration") ?></h6>
            <div class="form-group row">
                <label for="meta_title" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Title") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_title', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_title',
                        'placeholder' => __('Meta Title'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_keyword" style="width: 160px;" class="col-sm-2 col-form-label fw-bold"><?= __("Meta Keyword") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_keyword', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_keyword',
                        'placeholder' => __('Meta Keyword'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="meta_description" style="width: 160px;"
                    class="col-sm-2 col-form-label fw-bold"><?= __("Meta Description") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('meta_description', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'meta_description',
                        'placeholder' => __('Meta Description'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="url_key" style="width: 160px;"
                    class="col-sm-2 col-form-label fw-bold"><?= __("URL Key") ?></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('url_key', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'url_key',
                        'placeholder' => __('URL Key'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
                        <div class="row">
                            <div class="col-sm-9 offset-sm-3">
                                <button type="submit" class="btn btn-primary"
                                    <?php if (empty($selectedCountry)): ?>disabled title="<?= __('Please select a country from the header dropdown before adding a CMS page.') ?>"<?php endif; ?>
                                ><?= __("Save") ?></button>
                                <button type="reset" class="btn btn-secondary ms-2"><?= __("Reset") ?></button>
                                <a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'index']) ?>" class="btn btn-secondary ms-2"><?= __("Cancel") ?></a>
                            </div>
                        </div>

                        <?php echo $this->Form->end(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

    let allFiles = [];

    document.getElementById('imageInput').addEventListener('change', function (event) {

        var files = Array.from(event.target.files);

        if(files.length == 0)
        {
            allFiles = [];
            renderPreviews();
            return false;
        }

        var validFiles = [];
        var invalidFiles = [];

        let processedFiles = 0;
        let totalFiles = files.length;

        // let dimension = <?= $webImageMinWidth ?>+'x'+<?= $webImageMaxWidth ?>+' and '+<?= $webImageMinHeight ?>+'x'+<?= $webImageMaxHeight ?>

        files.forEach(function(file, index) {

            var fileExtension = file.name.split('.').pop().toLowerCase();
            var allowedExtensions = ['jpg', 'jpeg', 'png'];

            // Check if the file has a valid extension
            if (allowedExtensions.includes(fileExtension)) {
                var img = new Image();
                img.src = URL.createObjectURL(file);

                img.onload = function () {
                    var width = img.naturalWidth;
                    var height = img.naturalHeight;
                    var fileSize = file.size / 1024 / 1024;  // Convert file size to MB

                    // Validate file size (max 10MB) and dimensions (width: 300-400px, height: 200-320px)
                    if (fileSize <= <?= $webImageSize ?> && width >= <?= $webImageMinWidth ?> && width <= <?= $webImageMaxWidth ?> && height >= <?= $webImageMinHeight ?> && height <= <?= $webImageMaxHeight ?>) {
                        validFiles.push(file);  // Add to valid files array
                    } else {

                        invalidFiles.push({file: file.name, reason: '<?= __('Image dimensions should be between '.$webImageMinWidth.'x'.$webImageMaxWidth.' and '.$webImageMinHeight.'x'.$webImageMaxHeight) ?>'});
                    }

                    processedFiles++;

                    // When all files are processed, update the file input and show alerts
                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }

                };

                img.onerror = function () {
                    invalidFiles.push({file: file, reason: '<?= __('Unable to load image') ?>'});
                    processedFiles++;

                    if (processedFiles === totalFiles) {
                        finalizeFileProcessing(validFiles, invalidFiles);
                    }
                };
            }
            else
            {

                invalidFiles.push({file: file.name, reason: '<?= __('Invalid file type. Only image/jpg,image/jpeg,image/png,image/svg are allowed.') ?>'});

                processedFiles++;

                if (processedFiles === totalFiles) {
                    finalizeFileProcessing(validFiles, invalidFiles);
                }
            }
        });

    });

    // Finalize file processing
    function finalizeFileProcessing(validFiles, invalidFiles) {


        var html = "<ul>";
        for(var i=0; i < invalidFiles.length; i++)
        {
           html += `<li>${invalidFiles[i].file} - ${invalidFiles[i].reason}</li>`;
        }
        html += '</ul>';

        const wrapper = document.createElement('div');
        wrapper.innerHTML = html;

        if(invalidFiles.length > 0)
        {
            swal({
                title: "<?= __("Error") ?>",
                content: wrapper,
                icon: "error",
                confirmButtonText: "<?= __("OK") ?>",
                allowOutsideClick: "true"
            });
        }

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
        });

        document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();

    }

    // Function to update the file input with only valid files
    function updateFileInput(validFiles) {

        var dataTransfer = new DataTransfer();

        validFiles.forEach(function(file) {
            dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
        });

        document.getElementById('imageInput').files = dataTransfer.files;  // Update input files

        let newFiles = validFiles;
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
    }

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (file.url) {
                li.innerHTML = `
                        <img src="${file.url}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
            } else {
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                        <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                        <span class="image-name" title="${fileName}">${shortName}</span>
                        <button type="button" class="delete-img-btn" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            allFiles.splice(index, 1);

            //Remove file from input
            var dataTransfer = new DataTransfer();

            allFiles.forEach(function(file) {
                dataTransfer.items.add(file);  // Add valid files to the DataTransfer object
            });

            document.getElementById('imageInput').files = dataTransfer.files;

            renderPreviews();
        }
    });

    $(function () {
        // Common CKEditor configuration
        var ckeditorConfig = {
            height: 300,
            // Allow all HTML content including custom elements
            allowedContent: true,
            // Don't remove empty elements that might be needed for styling
            removeEmptyElements: false,
            // Allow all HTML5 elements
            extraAllowedContent: 'section[*]{*}(*);nav[*]{*}(*);ol[*]{*}(*);li[*]{*}(*);a[*]{*}(*);div[*]{*}(*)',
            // Configure toolbar
            toolbar: [
                { name: 'document', items: ['Source', '-', 'Save', 'NewPage', 'Preview', 'Print', '-', 'Templates'] },
                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
                { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll', '-', 'SpellChecker', 'Scayt'] },
                '/',
                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
                { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl'] },
                { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
                { name: 'insert', items: ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'] },
                '/',
                { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
                { name: 'colors', items: ['TextColor', 'BGColor'] },
                { name: 'tools', items: ['Maximize', 'ShowBlocks'] }
            ]
        };

        // Initialize English content editor
        CKEDITOR.replace("ckeditor", ckeditorConfig);

        // Initialize Arabic content editor with RTL support
        var arabicConfig = Object.assign({}, ckeditorConfig, {
            language: 'ar',
            contentsLangDirection: 'rtl'
        });
        CKEDITOR.replace("ckeditor_ar", arabicConfig);

        // Setup event handlers for all CKEditor instances
        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].on('blur', function () {
                CKEDITOR.instances[this.name].updateElement();
            });

            // Also update on key events to ensure validation works properly
            CKEDITOR.instances[instance].on('key', function () {
                var self = this;
                setTimeout(function() {
                    self.updateElement();
                }, 100);
            });
        }
    });

    $(document).ready(function () {

        $('.inputtags').tagsinput({
            confirmKeys: [44]
        });
    });

    var validationMessages = {
        categoryRequired: "<?= __('Please select content category') ?>",
        categoryIdentifierRequired: "<?= __('Please enter content category ID') ?>",
        titleRequired: "<?= __('Please enter title') ?>",
        tagRequired: "<?= __('Please enter tag name') ?>",
        publishDateRequired: "<?= __('Please choose published date') ?>",
        contentRequired: "<?= __('Please enter content') ?>",
        patternError: "<?= __('Only lowercase letters and underscores are allowed') ?>"
    };

    $(document).ready(function () {
        $.validator.addMethod("pattern", function (value, element) {
            return this.optional(element) || /^[a-z-]+$/.test(value);
        }, validationMessages.patternError);

        // Custom validation method for CKEditor
        $.validator.addMethod("ckeditorRequired", function (value, element) {
            // Update CKEditor content first
            if (CKEDITOR.instances[element.id]) {
                CKEDITOR.instances[element.id].updateElement();
                value = CKEDITOR.instances[element.id].getData();
            }

            // Remove HTML tags and check if there's actual content
            var textContent = value.replace(/<[^>]*>/g, '').trim();
            return textContent.length > 0;
        }, validationMessages.contentRequired);

        $("#add").validate({

            ignore: "",
            rules: {
                'content_category': {
                    required: true
                },
                'content_category_identifier': {
                    required: true
                },
                'title': {
                    required: true
                },
                'tag': {
                    required: true
                },
                'published_date': {
                    required: true
                },
                'content': {
                    ckeditorRequired: true
                }
            },
            messages: {
                'content_category': {
                    required: validationMessages.categoryRequired
                },
                'content_category_identifier': {
                    required: validationMessages.categoryIdentifierRequired
                },
                'title': {
                    required: validationMessages.titleRequired
                },
                'tag': {
                    required: validationMessages.tagRequired
                },
                'published_date': {
                    required: validationMessages.publishDateRequired
                },
                'content': {
                    ckeditorRequired: validationMessages.contentRequired
                }
            },
            submitHandler: function (form) {

                $('button[type="submit"]').attr('disabled', 'disabled');

                // Update all CKEditor instances before submitting
                for (instance in CKEDITOR.instances) {
                    CKEDITOR.instances[instance].updateElement();
                }

                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            }
        });
    });

    $(document).ready(function () {
        // Disable Save button if country not selected (enforced in JS as well)
        <?php if (empty($selectedCountry)): ?>
        $('button[type="submit"]').prop('disabled', true).attr('title', "<?= __('Please select a country from the header dropdown before adding a CMS page.') ?>");
        <?php endif; ?>
    });
</script>
<?php $this->end(); ?>
