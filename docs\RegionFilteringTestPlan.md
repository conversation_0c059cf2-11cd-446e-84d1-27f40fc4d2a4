# Region-Based Filtering Test Plan

## Overview
This document outlines the testing procedures for the region-based filtering system implemented across Brand, Category, Banners, CMS pages, and Widgets modules.

## Pre-Test Setup

### 1. Database Migration
```bash
# Run the migration to add country_id fields
bin/cake migrations migrate
```

### 2. Verify Super Admin Protection
- Ensure Super Admin role (ID: 2) cannot be deleted
- Ensure users with Super Admin role cannot be deleted
- Test role edit restrictions for Super Admin

### 3. Test Data Setup
- Create test countries (if not already present)
- Create test brands, categories, banners, content pages, and widgets
- Assign different countries to test records
- Create users with different role permissions

## Test Scenarios

### 1. Super Admin Role Protection Tests

**Test 1.1: Super Admin Role Deletion**
- Navigate to Roles management
- Attempt to delete Super Admin role (ID: 2)
- Expected: Error message "Super Admin role cannot be deleted"

**Test 1.2: Super Admin Role Modification**
- Navigate to Roles management
- Attempt to edit Super Admin role (ID: 2)
- Expected: Error message "Super Admin role cannot be modified"

**Test 1.3: Super Admin User Deletion**
- Navigate to Users management
- Attempt to delete a user with Super Admin role
- Expected: Error message "Super Admin user cannot be deleted"

### 2. Country Filtering Tests

**Test 2.1: Brand Country Filtering**
- Login as user with specific country access
- Navigate to Brands index
- Verify only brands from accessible countries are shown
- Test add/edit/delete operations with country restrictions

**Test 2.2: Category Country Filtering**
- Login as user with specific country access
- Navigate to Categories index
- Verify only categories from accessible countries are shown
- Test add/edit/delete operations with country restrictions

**Test 2.3: Banner Country Filtering**
- Login as user with specific country access
- Navigate to Banners index
- Verify only banners from accessible countries are shown
- Test add/edit/delete operations with country restrictions

**Test 2.4: Content Pages Country Filtering**
- Login as user with specific country access
- Navigate to Content Pages index
- Verify only content pages from accessible countries are shown
- Test add/edit/delete operations with country restrictions

**Test 2.5: Widgets Country Filtering**
- Login as user with specific country access
- Navigate to Widgets index
- Verify only widgets from accessible countries are shown
- Test add/edit/delete operations with country restrictions

### 3. Permission-Based Access Tests

**Test 3.1: Country Access Restriction**
- Login as user with limited country access
- Attempt to edit record from non-accessible country
- Expected: Redirect with error message "You do not have permission to access this [item]"

**Test 3.2: Country Assignment on Create**
- Login as user with specific country filter selected
- Create new brand/category/banner/content page/widget
- Verify country_id is automatically assigned based on session filter

**Test 3.3: All Countries Access**
- Login as user with "All Countries" access
- Verify all records are visible regardless of country assignment
- Test CRUD operations work without restrictions

### 4. Frontend Display Tests

**Test 4.1: Country Column Display**
- Navigate to each module index page
- Verify "Country" column is displayed in table header
- Verify country badges are shown correctly:
  - Green badge with map icon for specific countries
  - Gray badge with globe icon for global/unassigned

**Test 4.2: Country Filter Dropdown**
- Verify country filter dropdown in header works
- Test "All Countries" option
- Test individual country selection
- Verify session persistence across page navigation

## Test Cases by User Role

### Super Admin User
- Should see all records regardless of country
- Should be able to perform all CRUD operations
- Should not be deletable
- Role should not be modifiable

### Country-Specific User
- Should only see records from assigned country
- Should only be able to edit/delete accessible records
- New records should auto-assign to user's country filter
- Should get permission errors for non-accessible records

### Multi-Country User
- Should see records from all assigned countries
- Should be able to work with records from any assigned country
- Should not see records from non-assigned countries

## Expected Results

### Database Changes
- All tables (brands, categories, banners, content_pages, widgets) have country_id field
- Foreign key constraints are properly set
- Migration can be rolled back successfully

### Backend Functionality
- All controllers apply role-based country filtering
- Permission checks prevent unauthorized access
- Country assignment works automatically on create
- Super admin protection is enforced

### Frontend Display
- Country columns are visible in all index pages
- Country badges display correctly
- Country filter dropdown functions properly
- Session persistence works across modules

## Validation Checklist

- [ ] Database migration completed successfully
- [ ] Super admin role protection works
- [ ] Super admin user protection works
- [ ] Brand country filtering works
- [ ] Category country filtering works
- [ ] Banner country filtering works
- [ ] Content pages country filtering works
- [ ] Widget country filtering works
- [ ] Permission-based access restrictions work
- [ ] Country assignment on create works
- [ ] Frontend country columns display correctly
- [ ] Country filter dropdown functions
- [ ] Session persistence works
- [ ] All CRUD operations respect country permissions

## Troubleshooting

### Common Issues
1. **Migration fails**: Check database permissions and existing data
2. **Country not displaying**: Verify association is loaded in controller
3. **Permission errors**: Check user role and country assignments
4. **Frontend not updating**: Clear browser cache and check JavaScript console

### Debug Steps
1. Check error logs for detailed error messages
2. Verify database schema changes
3. Test with different user roles and permissions
4. Validate session data for country filter selection
