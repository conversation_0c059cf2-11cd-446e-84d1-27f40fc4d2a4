<?php
declare(strict_types=1);

namespace App\Model\Entity;

use Cake\ORM\Entity;

/**
 * Banner Entity
 *
 * @property int $id
 * @property string $title
 * @property string|null $summary
 * @property string|null $banner_location
 * @property string $banner_type
 * @property string|null $url_link
 * @property bool|null $display_in_web
 * @property bool|null $display_in_mobile
 * @property string|null $web_banner
 * @property string|null $mobile_banner
 * @property \Cake\I18n\Date|null $start_date
 * @property \Cake\I18n\Date|null $end_date
 * @property int $display_order
 * @property string $status
 * @property \Cake\I18n\DateTime $created
 * @property \Cake\I18n\DateTime $modified
 */
class Banner extends Entity
{
    /**
     * Fields that can be mass assigned using newEntity() or patchEntity().
     *
     * Note that when '*' is set to true, this allows all unspecified fields to
     * be mass assigned. For security purposes, it is advised to set '*' to false
     * (or remove it), and explicitly make individual fields accessible as needed.
     *
     * @var array<string, bool>
     */
    protected array $_accessible = [
        'title' => true,
        'title_ar' => true,
        'country_id' => true,
        'summary' => true,
        'summary_ar' => true,
        'banner_location' => true,
        'banner_type' => true,
        'url_link' => true,
        'target_mob' => true,
        'display_in_web' => true,
        'display_in_mobile' => true,
        'web_banner' => true,
        'mobile_banner' => true,
        'start_date' => true,
        'end_date' => true,
        'display_order' => true,
        'status' => true,
        'created' => true,
        'modified' => true,
    ];
}
