<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Widget $widget
 */
?>
<?php $this->append('style'); ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
    <style>
        input,
        select,
        textarea {
            width: 300px;
            padding: 5px;
            margin-bottom: 10px;
        }

        .is-invalid-ckeditor {
            border-color: #dc3545 !important;
            padding-right: calc(1.5em + .75rem);
            background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }

        .web-media-container {
            position: relative;
            width: 50%;
        }

        .mob-media-container {
            position: relative;
            width: 50%;
        }
    </style>
</head>
<?php $this->end(); ?>

<body>
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'index']) ?>">
                    <?= __('Widgets') ?>
                </a>
            </li>
            <li class="breadcrumb-item active">
                <?= __('Add') ?>
            </li>
        </ul>
        <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
            <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
        </a>
    </div>


    <div class="section-body">
        <div class="container-fluid">
            <div class="card">
                <?php echo $this->Form->create($widget, ['id' => 'addWidgetsForm', 'novalidate' => true, 'type' => 'post', 'enctype' => 'multipart/form-data']); ?>
                <h6 class="m-b-20" style="color: #004958">
                    <?= __('Add Widget') ?>
                </h6>
                <?php if (!empty($selectedCountry)): ?>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Assignment") ?></label>
                    <div class="col-sm-5">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            <?= __('This widget will be assigned to: <strong>{0}</strong>', h($selectedCountry->name)) ?>
                            <br>
                            <small class="text-muted">
                                <?= __('To change country, use the country dropdown in the header.') ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php elseif (empty($selectedCountry)): ?>
                <div class="form-group row">
                    <label class="col-sm-2 col-form-label fw-bold"><?= __("Country Required") ?></label>
                    <div class="col-sm-5">
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= __('Please select a country from the header dropdown before adding a widget.') ?>
                            <br>
                            <small class="text-muted">
                                <?= __('Widgets must be assigned to a specific country.') ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="form-group row">
                    <label for="widget-id" class="col-sm-2 col-form-label fw-bold"><?= __('Widget ID') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('widget_id_temp', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'widget-id',
                            'placeholder' => __('Enter Widget ID'),
                            'label' => false,
                            'readonly' => true,
                            'disabled' => true,
                            'value' => $nextId
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="widget-title" class="col-sm-2 col-form-label fw-bold"><?= __('Widget Title') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'widget-title',
                            'placeholder' => __('Enter Widget Title'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="widget-title_ar" class="col-sm-2 col-form-label fw-bold"><?= __('Widget Title (Arabic)') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('title_ar', [
                            'type' => 'text',
                            'class' => 'form-control',
                            'id' => 'widget-title_ar',
                            'placeholder' => __('Enter Widget Title (Arabic)'),
                            'label' => false,
                            'required' => true
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="widget-type" class="col-sm-2 col-form-label fw-bold"><?= __('Widget Type') ?><sup
                            class="text-danger font-11">*</sup>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('widget_type', [
                            'type' => 'select',
                            'id' => 'widget-type',
                            'options' => $widgetTypes,
                            'class' => 'form-control',
                            'label' => false,
                            'empty' => __('Select a Widget Type'),
                            'required' => true
                        ]) ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="ckeditor" class="col-sm-2 col-form-label fw-bold"><?= __("Widget Content") ?> <sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-10 main-field">
                        <?php echo $this->Form->control('summary', [
                            'type' => 'textarea',
                            'class' => 'form-control ckeditor-textarea',
                            'id' => 'ckeditor',
                            'label' => false,
                            'required' => true
                        ]); ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="ckeditor_ar" class="col-sm-2 col-form-label fw-bold"><?= __("Widget Content (Arabic)") ?> <sup
                            class="text-danger font-11">*</sup></label>
                    <div class="col-sm-10 main-field">
                        <?php echo $this->Form->control('summary_ar', [
                            'type' => 'textarea',
                            'class' => 'form-control ckeditor-textarea',
                            'id' => 'ckeditor_ar',
                            'label' => false,
                            'required' => true
                        ]); ?>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="web-image" class="col-sm-2 col-form-label fw-bold"><?= __('Web Image') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('web_image_file', [
                            'type' => 'file',
                            'class' => 'form-control',
                            'id' => 'web-image',
                            'placeholder' => __('Web Image'),
                            'accept' => implode(',', $webImageType),
                            'data-max-size' => '10MB',
                            'label' => false
                        ]); ?>
                        <span id="Image-validations" class="Image-validations"><small>Only <?= implode(', ', $webImageTypedisp) ?> files are allowed. Max size: <?= $webImageSize ?> MB. Dimensions: <?= $webImageMinWidth ?> x <?= $webImageMinHeight ?> and <?= $webImageMaxWidth ?> x <?= $webImageMaxHeight ?>.</small></span>
                        <div class="web-media-container">

                        </div>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="mobile-image" class="col-sm-2 col-form-label fw-bold"><?= __('Mobile Image') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('mobile_image_file', [
                            'type' => 'file',
                            'class' => 'form-control',
                            'id' => 'mobile-image',
                            'placeholder' => __('Mobile Image'),
                            'accept' => implode(',', $mobImageType),
                            'data-max-size' => '5MB',
                            'label' => false
                        ]); ?>
                        <span id="mob-Image-validations" class="Image-validations"><small>Only <?= implode(', ', $mobImageTypedisp) ?> files are allowed. Max size: <?= $mobImageSize ?> MB. Dimensions: <?= $mobImageMinWidth ?> x <?= $mobImageMinHeight ?> and <?= $mobImageMaxWidth ?> x <?= $mobImageMaxHeight ?>.</small></span>
                        <div class="mob-media-container">

                        </div>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="no-of-product" class="col-sm-2 col-form-label fw-bold"><?= __('No of Products') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('no_of_product', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'no-of-product',
                            'placeholder' => __('Enter No of Products'),
                            'label' => false,
                            'required' => false,
                            'value' => ''
                        ]); ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="product-preference" class="col-sm-2 col-form-label fw-bold"><?= __('Product Preference') ?>
                    </label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('product_preference', [
                            'type' => 'select',
                            'id' => 'product-preference',
                            'options' => $productPreference,
                            'class' => 'form-control',
                            'label' => false,
                            'empty' => __('Select Product Preference'),
                        ]) ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label for="catgory-id" class="col-sm-2 col-form-label fw-bold"><?= __('Catgeory(s)') ?></label>
                    <div class="col-sm-5">
                        <?php
                        $options = [];
                        foreach ($formattedCategories as $id => $category) {
                            $options[$id] = [
                                'text' => $category['text'],
                                'value' => $id,
                                'data-level' => $category['level']
                            ];
                        }

                        echo $this->Form->select('widget_category_mappings._ids', $options, [
                            'id' => 'category-id',
                            'class' => 'form-control select2',
                            'multiple' => 'multiple',
                            'label' => false,
                            'empty' => __('All')
                        ]);
                        ?>
                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>



                <div class="form-group row">
                    <label for="display-order" class="col-sm-2 col-form-label fw-bold"><?= __('Display Order') ?></label>
                    <div class="col-sm-5">
                        <?php echo $this->Form->control('display_order', [
                            'type' => 'number',
                            'class' => 'form-control',
                            'id' => 'display-order',
                            'placeholder' => __('Display Order'),
                            'label' => false,
                            'required' => false,
                            'value' => ''
                        ]); ?>

                        <div class="invalid-feedback">
                        </div>
                    </div>
                </div>



                <div class="form-group row">
                    <div class="col-sm-10 offset-sm-2">
                        <button type="submit" id="btnSubmit" class="btn"
                            <?php if (empty($selectedCountry)): ?>disabled title="<?= __('Please select a country from the header dropdown before adding a widget.') ?>"<?php endif; ?>
                        ><?= __('Save') ?></button>
                        <button type="reset" id="btnReset" class="btn"><?= __('Reset') ?></button>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
</body>


<?php $this->append('script'); ?>
<script type="text/javascript">
    const swalFailedTitle = "<?= addslashes(__('Error')); ?>";
    const swalInvalidFileType = "<?= addslashes(__('Invalid file type. Only ')); ?>";
    const swalFileSizeExceeded = "<?= addslashes(__('File size exceeds the maximum allowed size of')); ?>";
    const swalInvalidDimensions = "<?= addslashes(__('Image dimensions should be between')); ?>";
</script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('js/image.js') ?>"></script>

<script>
    $(document).ready(function() {


        $('.select2').select2({
            minimumResultsForSearch: 0
        });

        $('#categories-id').select2({
            allowClear: false,
            multiple: true,
            width: '100%'
        });

        $(function() {
            // Common CKEditor configuration
            var ckeditorConfig = {
                height: 300,
                // Allow all HTML content including custom elements
                allowedContent: true,
                // Don't remove empty elements that might be needed for styling
                removeEmptyElements: false,
                // Allow all HTML5 elements
                extraAllowedContent: 'section[*]{*}(*);nav[*]{*}(*);ol[*]{*}(*);li[*]{*}(*);a[*]{*}(*);div[*]{*}(*)',
                // Configure toolbar
                toolbar: [
                    { name: 'document', items: ['Source', '-', 'Save', 'NewPage', 'Preview', 'Print', '-', 'Templates'] },
                    { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', '-', 'Undo', 'Redo'] },
                    { name: 'editing', items: ['Find', 'Replace', '-', 'SelectAll', '-', 'SpellChecker', 'Scayt'] },
                    '/',
                    { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Subscript', 'Superscript', '-', 'RemoveFormat'] },
                    { name: 'paragraph', items: ['NumberedList', 'BulletedList', '-', 'Outdent', 'Indent', '-', 'Blockquote', 'CreateDiv', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock', '-', 'BidiLtr', 'BidiRtl'] },
                    { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
                    { name: 'insert', items: ['Image', 'Flash', 'Table', 'HorizontalRule', 'Smiley', 'SpecialChar', 'PageBreak', 'Iframe'] },
                    '/',
                    { name: 'styles', items: ['Styles', 'Format', 'Font', 'FontSize'] },
                    { name: 'colors', items: ['TextColor', 'BGColor'] },
                    { name: 'tools', items: ['Maximize', 'ShowBlocks'] }
                ]
            };

            // Initialize English content editor
            CKEDITOR.replace("ckeditor", ckeditorConfig);

            // Initialize Arabic content editor with RTL support
            var arabicConfig = Object.assign({}, ckeditorConfig, {
                language: 'ar',
                contentsLangDirection: 'rtl'
            });
            CKEDITOR.replace("ckeditor_ar", arabicConfig);

            // Setup event handlers for all CKEditor instances
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].on('blur', function() {
                    CKEDITOR.instances[this.name].updateElement();
                });

                // Also update on key events to ensure validation works properly
                CKEDITOR.instances[instance].on('key', function() {
                    var self = this;
                    setTimeout(function() {
                        self.updateElement();
                    }, 100);
                });
            }
        });

        function validateForm() {
            let isValid = true;

            $('#addWidgetsForm').find('input[required], select[required], #ckeditor, #ckeditor_ar').each(function() {
                let value;
                let isckEditor = $(this).hasClass('ckeditor-textarea');

                if (isckEditor) {
                    value = CKEDITOR.instances[$(this).attr('id')].getData().trim();
                } else {
                    value = $(this).val().trim();
                }

                if (value === '') {
                    $(this).addClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    let fieldName = $(this).closest('.form-group').find('label').text().trim().replace(/\*$/, '');
                    feedback.text('<?= __("Please enter ") ?>' + fieldName.toLowerCase() + '.').show();
                    isValid = false;

                    if (isckEditor) {
                        $(this).closest('.form-group').find('.cke').addClass('is-invalid-ckeditor');
                        $(this).closest('.form-group').find('.error-message').remove();
                        let errorMessage = '';
                        if ($(this).attr('id') === 'ckeditor_ar') {
                            errorMessage = '<?= __("Please enter widget content (Arabic).") ?>';
                        } else {
                            errorMessage = '<?= __("Please enter widget content.") ?>';
                        }
                        $(this).closest('.form-group').append('<div class="row"><div class="col-sm-2"></div><div class="col-sm-6"><span class="error-message" style="color:#dc3545;margin-left:5px;font-size: .875em">' + errorMessage + '</span></div></div>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    let feedback = $(this).closest('.form-group').find('.invalid-feedback');
                    feedback.hide();

                    if (isckEditor) {
                        $(this).closest('.form-group').find('.cke').removeClass('is-invalid-ckeditor');
                        $(this).closest('.form-group').find('.error-message').hide();
                    }
                }
            });


            return isValid;
        }

        $('#btnSubmit').click(function(event) {
            event.preventDefault();
            if (!validateForm()) {
                return;
            }
            var form = $('#addWidgetsForm')[0];
            form.action = "<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'add']) ?>";
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].updateElement();
            }
            $('#btnSubmit').attr('disabled', true);
            form.submit();
        });

        $('#btnReset').click(function(event) {
            CKEDITOR.instances['ckeditor'].setData('');
            CKEDITOR.instances['ckeditor_ar'].setData('');
        });

        $('#web-image').on('change', async function() {
            let isValid = false;
            $('.web-media-container').empty();


            isValid = await asyncvalidateFile(
                this,
                <?= $webImageSize ?> * 1024 * 1024,
                <?= json_encode($webImageType) ?>,
                <?= $webImageMinWidth ?>,
                <?= $webImageMaxWidth ?>,
                <?= $webImageMinHeight ?>,
                <?= $webImageMaxHeight ?>
            );


            if (isValid) {
                renderMediaPreview();
            }
        });

        $('#mobile-image').on('change', async function() {
            let isValid = false;
            $('.mob-media-container').empty();
            isValid = await asyncvalidateFile(
                this,
                <?= $mobImageSize ?> * 1024 * 1024,
                <?= json_encode($mobImageType) ?>,
                <?= $mobImageMinWidth ?>,
                <?= $mobImageMaxWidth ?>,
                <?= $mobImageMinHeight ?>,
                <?= $mobImageMaxHeight ?>
            );

            if (isValid) {
                renderMediaPreview();
            }
        });

        function renderMediaPreview() {

            $('.web-media-container').empty();
            $('.mob-media-container').empty();
            const webFile = $('#web-image')[0].files[0];
            const mobileFile = $('#mobile-image')[0].files[0];

            if (webFile) {
                const webReader = new FileReader();
                const webfileName = $('#web-image')[0].files[0].name;
                webReader.onload = function(e) {
                    $('.web-media-container').html(`<img src="${e.target.result}" alt="Web Media Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Web Media Preview">${webfileName}</span>
                    <button type="button" class="delete-img-btn delete-web-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                webReader.readAsDataURL(webFile);
            }

            if (mobileFile) {
                const mobileReader = new FileReader();
                const mobilefileName = $('#mobile-image')[0].files[0].name;
                mobileReader.onload = function(e) {
                    $('.mob-media-container').html(`
                    <img src="${e.target.result}" alt="Mobile Media Preview" style="max-width:100%; height:auto;">
                    <span class="image-name" title="Mobile Media Preview" >${mobilefileName}</span>
                    <button type="button" class="delete-img-btn delete-mob-media">
                    <i class="fas fa-times"></i>
                    </button>
                    `);
                };
                mobileReader.readAsDataURL(mobileFile);
            }
        }

        $(document).on('click', '.delete-web-media', function(event) {
            $('.web-media-container').empty();
            $('#web-image').val('');
        });

        $(document).on('click', '.delete-mob-media', function(event) {
            $('.mob-media-container').empty();
            $('#mobile-image').val('');
        });

        // Disable Save button if country not selected (enforced in JS as well)
        <?php if (empty($selectedCountry)): ?>
        $('#btnSubmit').prop('disabled', true).attr('title', "<?= __('Please select a country from the header dropdown before adding a widget.') ?>");
        <?php endif; ?>
    });
</script>
<?php $this->end(); ?>