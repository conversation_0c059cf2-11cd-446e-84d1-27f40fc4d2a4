<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\User $user
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/intlTelInput.css'); ?>" />
<?php $this->end(); ?>
<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><?= __("Users") ?></li>
        <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']) ?>"><?= __("Manage Users") ?></a>
        </li>
        <li class="breadcrumb-item active"><?= __("Edit Profile") ?></li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __("BACK") ?></small>
    </a>
</div>
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <h6 class="m-b-20" style="color: #206331"><?= __("Edit User Profile") ?></h6>
            <?php echo $this->Form->create($curUser, ['id' => 'add', 'novalidate' => true, 'type' => 'file']); ?>
            <div class="form-group row">
                <label for="first_name" class="col-sm-2 col-form-label fw-bold"><?= __("Role") ?> <sup
                        class="text-danger font-11">*</sup></label>
                    <div class="col-sm-5 main-field">
                    <?php if (isset($isEditingSelf) && $isEditingSelf): ?>
                        <?= $this->Form->control('role_id', [
                            'type' => 'select',
                            'options' => $roles,
                            'class' => 'form-control form-select',
                            'id' => 'role_id',
                            'label' => false,
                            'empty' => __('Select Role'),
                            'disabled' => true
                        ]); ?>
                        <small class="text-muted"><?= __('You cannot change your own role') ?></small>
                    <?php else: ?>
                        <?= $this->Form->control('role_id', [
                            'type' => 'select',
                            'options' => $roles,
                            'class' => 'form-control form-select',
                            'id' => 'role_id',
                            'label' => false,
                            'empty' => __('Select Role')
                        ]); ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="first_name" class="col-sm-2 col-form-label fw-bold"><?= __("First Name") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('first_name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'first_name',
                        'placeholder' => __('First Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="last_name" class="col-sm-2 col-form-label fw-bold"><?= __("Last Name") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php echo $this->Form->control('last_name', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'id' => 'last_name',
                        'placeholder' => __('Last Name'),
                        'label' => false
                    ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="mobile" class="col-sm-2 col-form-label fw-bold"><?= __("Mobile") ?></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('mobile_no', [
                        'type' => 'tel',
                        'class' => 'form-control',
                        'id' => 'mobile_no',
                        'placeholder' => __('Mobile'),
                        'label' => false
                    ]); ?>
                    <?php echo $this->Form->control('mobile_full', [
                    'type' => 'hidden',
                    'class' => 'form-control',
                    'id' => 'mobile_full',
                    'label' => false
                ]); ?>
                </div>
            </div>
            <div class="form-group row">
                <label for="email" class="col-sm-2 col-form-label fw-bold"><?= __("Email") ?> <sup
                        class="text-danger font-11">*</sup></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('email', [
                        'type' => 'email',
                        'class' => 'form-control',
                        'id' => 'email',
                        'placeholder' => __('Email'),
                        'label' => false,
                        'disabled' => 'disabled'
                    ]);
                    ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="new_password" class="col-sm-2 col-form-label fw-bold"><?= __("New Password") ?></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('new_password', [
                        'type' => 'password',
                        'class' => 'form-control',
                        'id' => 'new_password',
                        'placeholder' => __('New Password'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="confirm_new_password" class="col-sm-2 col-form-label fw-bold"><?= __("Confirm New Password") ?></label>
                <div class="col-sm-5 main-field">
                    <?php
                    echo $this->Form->control('confirm_new_password', [
                        'type' => 'password',
                        'class' => 'form-control',
                        'id' => 'confirm_new_password',
                        'placeholder' => __('Confirm New Password'),
                        'label' => false
                    ]); ?>
                </div>
            </div>

            <div class="form-group row">
                <label for="department" class="col-sm-2 col-form-label fw-bold"><?= __("Department") ?></label>
                <div class="col-sm-5 main-field">
                <?php
                    echo $this->Form->control('department', [
                        'type' => 'text',
                        'class' => 'form-control',
                        'list' => 'department-list',
                        'id' => 'department',
                        'placeholder' => __('Department'),
                        'label' => false,
                        'autocomplete' => 'off'
                    ]);
                    ?>
                    <datalist id="department-list">
                        <?php foreach ($departments as $department): ?>
                            <option value="<?php echo h($department->department); ?>">
                                <?php echo h($department->department); ?>
                            </option>
                        <?php endforeach; ?>
                    </datalist>
                </div>
            </div>
            <div class="form-group row">
                <label for="status" class="col-sm-2 col-form-label fw-bold"><?= __("Status") ?></label>
                <div class="col-sm-5 main-field">
                    <?php if (isset($isEditingSelf) && $isEditingSelf): ?>
                        <?php echo $this->Form->control('status', [
                            'type' => 'select',
                            'class' => 'form-control form-select',
                            'id' => 'status',
                            'options' => [
                                'A' => __('Active'),
                                'I' => __('Inactive')
                            ],
                            'empty' => __('Select Status'),
                            'label' => false,
                            'disabled' => true
                        ]); ?>
                        <small class="text-muted"><?= __('You cannot change your own status') ?></small>
                    <?php else: ?>
                        <?php echo $this->Form->control('status', [
                            'type' => 'select',
                            'class' => 'form-control form-select',
                            'id' => 'status',
                            'options' => [
                                'A' => __('Active'),
                                'I' => __('Inactive')
                            ],
                            'empty' => __('Select Status'),
                            'label' => false
                        ]); ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="form-group row">
                <div class="col-sm-10 offset-sm-2">
                    <button type="submit" class="btn btn-primary"><?= __("Save") ?></button>
                </div>
            </div>
        </div>
        </form>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('js/intlTelInputWithUtils.js'); ?>"></script>
<script>

    const input = document.querySelector("#mobile_no");
    const iti = window.intlTelInput(input, {
        initialCountry: "CI",
        showFlags: true,
        separateDialCode: true,
    });
    window.iti = iti;

    input.addEventListener('input', function () {
        const selectedData = iti.getSelectedCountryData();
        document.querySelector("#mobile_full").value = iti.getNumber();
    });

    $(document).ready(function () {

        $.validator.addMethod("pattern", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            if (typeof param === "string") {
                param = new RegExp("^(?:" + param + ")$");
            }
            return param.test(value);
        }, "Invalid format.");

        $.validator.addMethod("alphabetic", function (value, element) {
            return this.optional(element) || /^[a-zA-Z]+$/.test(value);
        }, "Please enter only alphabetic characters.");

        $.validator.addMethod('customEmail', function (value, element) {
            return this.optional(element) || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        }, 'Please enter a valid email address.');

        $.validator.addMethod("phoneNumber", function (value, element) {
            const numericValue = value.replace(/\D/g, '');
            return this.optional(element) || /^\d{10}$/.test(numericValue);
        }, "Please enter a valid 10-digit number.");

        $("#add").validate({
            ignore: "",
            rules: {
                'role_id': {
                    <?php if (isset($isEditingSelf) && $isEditingSelf): ?>
                    required: false
                    <?php else: ?>
                    required: true
                    <?php endif; ?>
                },
                'first_name': {
                    required: true,
                    alphabetic: true
                },
                'last_name': {
                    required: true,
                    alphabetic: true
                },
                'mobile_no': {
                    phoneNumber: true
                },
                'email': {
                    required: true,
                    customEmail: true
                },
                'new_password': {
                    minlength: 6,
                    required: false
                },
                'confirm_new_password': {
                    equalTo: '#new_password',
                    required: function() {
                        return $('#new_password').val().length > 0;
                    }
                }
            },
            messages: {
                'role_id': {
                    required: "Please select role"
                },
                'first_name': {
                    required: "Please enter first name"
                },
                'last_name': {
                    required: "Please enter last name"
                },
                'mobile_no': {
                    pattern: "Please enter a valid mobile number"
                },
                'email': {
                    required: "Please enter email address"
                },
                'new_password': {
                    minlength: "Password must be at least 6 characters long"
                },
                'confirm_new_password': {
                    equalTo: "Passwords do not match",
                    required: "Please confirm your new password"
                }
            },
            submitHandler: function (form) {

                document.querySelector("#mobile_full").value = iti.getNumber();
                
                $('button[type="submit"]').attr('disabled', 'disabled');
                form.submit();
            },
            errorPlacement: function (error, element) {
                error.appendTo(element.closest(".main-field"));
            },
            highlight: function (element) {
                $(element).addClass('is-invalid');
            },
            unhighlight: function (element) {
                $(element).removeClass('is-invalid');
            }
        });
    });
</script>
<?php $this->end(); ?>