<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\Behavior\TreeBehavior;
use Cake\Http\ServerRequestFactory;

/**
 * Categories Model
 *
 * @property \App\Model\Table\CategoriesTable&\Cake\ORM\Association\BelongsTo $ParentCategories
 * @property \App\Model\Table\BannerAdsTable&\Cake\ORM\Association\HasMany $BannerAds
 * @property \App\Model\Table\BrandCategoryMappingsTable&\Cake\ORM\Association\HasMany $BrandCategoryMappings
 * @property \App\Model\Table\CategoriesTable&\Cake\ORM\Association\HasMany $ChildCategories
 * @property \App\Model\Table\CategoryAttributesTable&\Cake\ORM\Association\HasMany $CategoryAttributes
 * @property \App\Model\Table\ProductCategoriesTable&\Cake\ORM\Association\HasMany $ProductCategories
 * @property \App\Model\Table\WidgetCategoryMappingsTable&\Cake\ORM\Association\HasMany $WidgetCategoryMappings
 *
 * @method \App\Model\Entity\Category newEmptyEntity()
 * @method \App\Model\Entity\Category newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Category> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Category get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Category findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Category patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Category> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Category|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Category saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Category>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Category>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Category>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Category> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Category>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Category>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Category>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Category> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CategoriesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */

    protected $language;
    protected $country;
    protected $country_id;

    public function initialize(array $config): void
    {
        parent::initialize($config);
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        $this->setTable('categories');
        $this->setDisplayField('name');
        $this->setPrimaryKey('id');

        $this->addBehavior('Tree', [
            'level' => 'level',
        ]);

        $this->addBehavior('Timestamp');

        $this->belongsTo('ParentCategories', [
            'className' => 'Categories',
            'foreignKey' => 'parent_id',
            'propertyName' => 'parent_category',
        ]);
        $this->hasMany('BannerAds', [
            'foreignKey' => 'category_id',
        ]);
        $this->hasMany('BrandCategoryMappings', [
            'foreignKey' => 'category_id',
        ]);
        $this->hasMany('ChildCategories', [
            'className' => 'Categories',
            'foreignKey' => 'parent_id',
            'dependent' => true
        ]);
        $this->hasMany('CategoryAttributes', [
            'foreignKey' => 'category_id',
            'className' => 'CategoryAttributes'
        ]);
        $this->hasMany('ProductCategories', [
            'foreignKey' => 'category_id',
        ]);
        $this->hasMany('WidgetCategoryMappings', [
            'foreignKey' => 'category_id',
        ]);

        $this->belongsToMany('Widgets', [
            'joinTable' => 'widget_category_mappings',
            'foreignKey' => 'category_id',
            'targetForeignKey' => 'widget_id',
            'through' => 'WidgetCategoryMappings',
        ]);

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('url_key')
            ->maxLength('url_key', 255)
            ->allowEmptyString('url_key');

        $validator
            ->scalar('name')
            ->maxLength('name', 255)
            ->requirePresence('name', 'create')
            ->notEmptyString('name');

        $validator
            ->scalar('name_ar')
            ->maxLength('name_ar', 255)
            ->requirePresence('name_ar', 'create')
            ->notEmptyString('name_ar');

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('description_ar')
            ->allowEmptyString('description_ar');

        $validator
            // ->scalar('category_icon')
            // ->maxLength('category_icon', 255)
            // ->requirePresence('category_icon', 'create')
            ->allowEmptyFile('category_icon');

        $validator
            ->maxLength('web_banner', 255)
            ->allowEmptyFile('web_banner');

        $validator
            ->integer('parent_id')
            ->allowEmptyString('parent_id');

        $validator
            ->boolean('is_featured')
            ->allowEmptyString('is_featured');

        $validator
            ->allowEmptyString('display_order');

        $validator
            ->scalar('meta_title')
            ->maxLength('meta_title', 255)
            ->allowEmptyString('meta_title');

        $validator
            ->scalar('meta_keyword')
            ->maxLength('meta_keyword', 255)
            ->allowEmptyString('meta_keyword');

        $validator
            ->scalar('meta_description')
            ->allowEmptyString('meta_description');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['parent_id'], 'ParentCategories'), ['errorField' => 'parent_id']);

        return $rules;
    }

    //S
    public function getCategorySuggestions($query)
    {
        return $this->find('all')
            ->select(['id', 'name', 'type' => "'category'"])
            ->where(['name LIKE' => '%' . $query . '%'])
            ->limit(10)
            ->toArray();
    }

    //S
    public function allCategories()
    {

        $allCategories = $this->find('all')
            ->select(['id', 'name', 'url_key', 'category_icon'])
            ->contain([
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'parent_id', 'url_key', 'display_order', 'category_icon']); // Select specific columns for subcategories
                }
            ])
            ->where(['Categories.status' => 'A', 'Categories.parent_id IS' => null])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        return $allCategories;
    }

    //S
    public function parentCategories()
    {

        $parentCategories = $this->find('all')
            ->select(['id', 'name', 'category_icon', 'url_key', 'category_icon'])
            ->where(['Categories.status' => 'A', 'Categories.parent_id IS' => null])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        return $parentCategories;
    }

    //S
    public function subCategories($parent_id)
    {

        // $subCategories = $this->find('all')
        //     ->select(['id', 'name', 'url_key', 'category_icon'])
        //     ->where(['Categories.status' => 'A', 'Categories.parent_id' => $parent_id])
        //     ->order(['Categories.display_order' => 'ASC'])
        //     ->toArray();

        // return $subCategories;

        $subCategories = $this->find('all')
            ->select(['id', 'name', 'url_key', 'category_icon'])
            ->where(['Categories.status' => 'A', 'Categories.parent_id' => $parent_id])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        foreach ($subCategories as &$subCategory) {
            $subCategory['children'] = $this->subCategories($subCategory['id']);
        }

        return $subCategories;
    }

    //S
    public function featuredCategories()
    {

        $parentCategories = $this->find('all')
            ->select(['id', 'name', 'url_key', 'category_icon'])
            ->where(['Categories.status' => 'A', 'Categories.is_featured' => 1])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        return $parentCategories;
    }

    public function getCategoryTree1($parent = null, $spacing = '', $userTreeArray = [])
    {
        $query = $this->find('all')
            ->select(['id', 'name', 'parent_id', 'display_order'])
            ->contain([
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'parent_id', 'display_order']);
                }
            ])
            ->order(['Categories.display_order' => 'ASC']);

        if (is_null($parent)) {
            $query->where(['Categories.parent_id IS' => null]);
        } else {
            $query->where(['Categories.parent_id' => $parent]);
        }

        $categories = $query->toArray();

        foreach ($categories as $category) {
            $userTreeArray[$category->id] = $spacing . $category->name;
            if (!empty($category->child_categories)) {
                $userTreeArray = $this->getCategoryTree($category->id, $spacing . '» ', $userTreeArray);
            }
        }
        return $userTreeArray;
    }

    public function getCategoryTree($parent = null, $spacing = '', $userTreeArray = [], $currentDepth = 0, $maxDepth = 2)
    {
        // Query to get all categories with their child categories
        $query = $this->find('all')
            ->select(['id', 'name', 'parent_id', 'display_order'])
            ->contain([
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'parent_id', 'display_order']);
                }
            ])
            ->order(['Categories.display_order' => 'ASC']);

        if (is_null($parent)) {
            $query->where(['Categories.parent_id IS' => null]);
        } else {
            $query->where(['Categories.parent_id' => $parent]);
        }

        $categories = $query->toArray();
        // echo "<pre>";
        // print_r($categories);
        // print_r($currentDepth);
        foreach ($categories as $category) {
            // Add the current category to the result array
            $userTreeArray[$category->id] = $spacing . $category->name;

            // Check if we are below the maximum depth level
            if (!empty($category->child_categories) && $currentDepth < $maxDepth) {
                // Process each child category
                foreach ($category->child_categories as $child) {
                    // Append the child category under the parent
                    $userTreeArray = $this->getCategoryTree($child->id, $spacing . '» ', $userTreeArray, $currentDepth + 1, $maxDepth);
                }
            }
        }
        // die;
        return $userTreeArray;
    }


    public function getCategoriesWithHierarchy()
    {
        $categories = $this->find('all')
            ->select([
                'id',
                'name',
                'parent_id',
                'status',
                'country_id'
            ])
            ->contain([
                'Countries' => function ($q) {
                    return $q->select(['id', 'name']);
                },
                'ParentCategories' => function ($q) {
                    return $q->select(['id', 'name']);
                },
                'ChildCategories' => function ($q) {
                    return $q->select(['id', 'name', 'parent_id', 'status', 'country_id'])
                        ->contain([
                            'Countries' => function ($q) {
                                return $q->select(['id', 'name']);
                            },
                            'ParentCategories' => function ($q) {
                                return $q->select(['id', 'name']);
                            },
                            'ChildCategories' => function ($q) {
                                return $q->select(['id', 'name', 'parent_id', 'status', 'country_id'])
                                    ->contain([
                                        'Countries' => function ($q) {
                                            return $q->select(['id', 'name']);
                                        },
                                        'ParentCategories' => function ($q) {
                                            return $q->select(['id', 'name']);
                                        }
                                    ]);
                            }
                        ]);
                }
            ])
            ->where(['Categories.parent_id IS' => null])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        return $categories;
    }

    public function getBreadcrumb($categoryId)
    {
        $breadcrumb = [];
        $category = $this->get($categoryId, ['contain' => ['ParentCategories']]);

        while ($category) {
            $breadcrumb[] = [
                'id' => $category->id,
                'name' => $category->name
            ];
            $category = $category->parent_category; // Assuming parent category is set up with self-referencing association
        }

        // Reverse to make it top-down
        return array_reverse($breadcrumb);
    }

    public function getSingleProductCategory()
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
        if ($isArabic) {
            $select_col = [
                'id',
                'name' => 'Categories.name_ar',
                'url_key',
                'description' => 'Categories.description_ar'
            ];
        } else {
            $select_col = [
                'id',
                'name' => 'Categories.name',
                'url_key',
                'description'
            ];
        }
        return $this->find()
            ->select($select_col)
            ->where(['Categories.parent_id IS' => null, 'Categories.status' => 'A', 'Categories.country_id' => $this->country_id])
            ->contain([
                'CategoryAttributes' => function ($q) {
                    return $q->select(['category_id', 'attribute_id', 'id'])
                        ->where(['CategoryAttributes.status' => 'A'])
                        ->contain([
                            'Attributes' => function ($q) {
                                return $q->select(['id', 'name']) // adjust fields
                                    ->contain([
                                        'AttributeValues' => function ($q) {
                                            return $q->select(['id', 'attribute_id', 'value', 'status'])
                                                ->where(['AttributeValues.status' => 'A']);
                                        }
                                    ]);
                            }
                        ]);
                }
            ])
            ->order(['Categories.name' => 'ASC'])
            // ->distinct(['Categories.id'])
            ->toArray();
    }
    // public function getSingleProductCategory()
    // {
    //     $query = $this->find('all');
    //     $query->select(['id' => 'Categories.id', 'name' => 'Categories.name','url_key'])
    //         ->distinct(['Categories.id'])
    //         ->where(['Categories.parent_id IS' => null])
    //         ->order(['Categories.name' => 'ASC']);

    //     return $query->toArray();
    // }



    public function subCategoriesLevel($parent_id = null)
    {
        $subCategories = $this->find('all')
            ->select(['id', 'name', 'url_key', 'category_icon'])
            ->where(['Categories.status' => 'A', 'Categories.parent_id IS NOT' => null])
            ->order(['Categories.display_order' => 'ASC'])
            ->toArray();

        foreach ($subCategories as &$subCategory) {
            $subCategory['children'] = [];
        }

        return $subCategories;
    }

}
