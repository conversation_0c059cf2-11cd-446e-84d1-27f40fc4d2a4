<?php
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\Showroom $showroom
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<?php $this->end(); ?>
<style type="text/css">
    .content {
        color: black;
    }
</style>
<section class="section">
        <div class="section-header d-flex justify-content-between align-items-center mb-3">
            <ul class="breadcrumb breadcrumb-style mb-0">
                <li class="breadcrumb-item">
                    <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
                </li>
                <li class="breadcrumb-item active">
                    <a
                href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'index']) ?>"><?= __('CMS Pages') ?></a>
                </li>
                <li class="breadcrumb-item active"><?= __('View') ?></li>
            </ul>
            <button onclick="history.back();" class="d-flex align-items-center" id="back-button-mo">
                <small class="p-10 fw-bold"><?= __('BACK') ?></small>
                <span class="rotate me-2">⤣</span>
            </button>
        </div>
    </section>

    <h5 class="m-l-10 p-t-10 p-b-10" style="color: black;"><?= __('View CMS Page') ?></h5>

    <section id="view-product-product-details" class="section-body">

            <div class="row">
            <div class="col-12 col-md-6 col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="form-group row">
                            <label for="category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Content Category') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->content_category) ? h($content->content_category) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="sub-category" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Content Category ID') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->content_category_identifier) ? h($content->content_category_identifier) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Title') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->title) ? h($content->title) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-size" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Title (Arabic)') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->title_ar) ? h($content->title_ar) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-weight" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('CMS Tag Name') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->tag) ? h($content->tag) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Description') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->description) ? h($content->description) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="brand" class="col-sm-2 col-form-label fw-bold  ps-5"><?= __('Description (Arabic)') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->description_ar) ? h($content->description_ar) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-title" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Image(s)') ?> </label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5">
                                    <div id="previeContainer" class="ps-5">
                                        <ul id="imagePreviewContainer">
                                        </ul>
                                    </div>
                                </p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-description" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Image AltTag') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->image_alt) ? h($content->image_alt) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-description" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Video URL') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->video_url) ? h($content->video_url) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Status') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;">
                                    <?php if(!empty($content->status)) {

                                    $status = $content->status;

                                    $statusLabel = ($status === 'A') ? 'Active' :
                                                   (($status === 'I') ? 'Inactive' : 'Deleted');

                                    echo $statusLabel;

                                    } ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Published') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->published) ? h($content->published) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="start-date" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Published Date') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->published_date) ? $content->published_date->format($dateFormat . ' ' . $timeFormat) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="product-tags" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Content') ?></label>
                            <div class="col-sm-10 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo !empty($content->content) ? $this->Html->div('content ps-5', $content->content, ['escape' => false]) : '-'; ?></p>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="content-ar" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Content (Arabic)') ?></label>
                            <div class="col-sm-10 ps-5">
                                <p class="ps-5" style="color: black; direction: rtl; text-align: right;"><?php echo !empty($content->content_ar) ? $this->Html->div('content ps-5', $content->content_ar, ['escape' => false]) : '-'; ?></p>
                            </div>
                        </div>

                        <h6 class="mt-5 m-b-25 ps-5" style="color: #004958">
                            SEO Configuration
                        </h6>

                        <div class="form-group row ps-5">
                            <label for="meta-title" class="col-sm-2 col-form-label fw-bold">Meta Title</label>
                            <div class="col-sm-5 ps-5">
                                <p style="color: black;"><?php echo !empty($content->seo_title) ? h($content->seo_title) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row ps-5">
                            <label for="meta-keywords" class="col-sm-2 col-form-label fw-bold">Meta Keywords</label>
                            <div class="col-sm-5 ps-5">
                                <p style="color: black;"><?php echo !empty($content->seo_keyword) ? h($content->seo_keyword) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row ps-5">
                            <label for="meta-description" class="col-sm-2 col-form-label fw-bold">Meta Description</label>
                            <div class="col-sm-5 ps-5">
                                <p style="color: black;"><?php echo !empty($content->meta_description) ? h($content->meta_description) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row ps-5">
                            <label for="meta-description" class="col-sm-2 col-form-label fw-bold">URL Key</label>
                            <div class="col-sm-5 ps-5">
                                <p style="color: black;"><?php echo !empty($content->url_key) ? h($content->url_key) : '-'; ?></p>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label for="country" class="col-sm-2 col-form-label fw-bold ps-5"><?= __('Country') ?></label>
                            <div class="col-sm-5 ps-5">
                                <p class="ps-5" style="color: black;"><?php echo isset($content->country) && !empty($content->country->name) ? h($content->country->name) : '-'; ?></p>
                            </div>
                        </div>
            </div>
            </div>
    </section>
<script>
    <?php
    $images = !empty($content->content_images) ? array_map(function ($image) {
        return [
            'id' => $image->id,
            'url' => $image->image
        ];
    }, $content->content_images) : [];
    ?>

    const existingImages = <?php echo json_encode($images, JSON_HEX_TAG); ?>;

    let allFiles = [];
    let deletedImages = [];

    function initializeExistingImages(existingImages) {
        existingImages.forEach((image, index) => {
            let file = {
                id: image.id,
                name: `existing-image-${index}.jpg`,
                type: 'image/jpeg',
                url: image.url
            };
            allFiles.push(file);
        });
        renderPreviews();
    }

    initializeExistingImages(existingImages);

    document.getElementById('imageInput').addEventListener('change', function (event) {
        let newFiles = Array.from(event.target.files);
        allFiles = [...allFiles, ...newFiles];
        renderPreviews();
        updateFileInput();
    });

    function renderPreviews() {
        let previewContainer = document.getElementById('imagePreviewContainer');
        previewContainer.innerHTML = '';

        allFiles.forEach((file, index) => {
            let li = document.createElement('li');
            li.classList.add('image-thumbnail');

            let fileName = file.name;
            let extension = fileName.slice((fileName.lastIndexOf(".") - 1 >>> 0) + 2);
            let nameWithoutExtension = fileName.slice(0, fileName.length - extension.length - 1);

            let shortName = nameWithoutExtension.length > 14 ? nameWithoutExtension.slice(0, 11) + '...' : nameWithoutExtension;
            shortName += '.' + extension;

            if (file.url) {
                li.innerHTML = `
                <img src="${file.url}" alt="Image Preview" class="preview-img"/>
            `;
            } else {
                let reader = new FileReader();
                reader.onload = function (e) {
                    li.innerHTML = `
                    <img src="${e.target.result}" alt="Image Preview" class="preview-img"/>
                `;
                };
                reader.readAsDataURL(file);
            }

            previewContainer.appendChild(li);
        });
    }

    document.getElementById('imagePreviewContainer').addEventListener('click', function (e) {
        if (e.target.closest('.delete-img-btn')) {
            let index = e.target.closest('.delete-img-btn').getAttribute('data-index');
            let removedFile = allFiles.splice(index, 1)[0];
            if (removedFile.url && removedFile.id) {
                deletedImages.push(removedFile.id);
            }

            renderPreviews();
            updateFileInput();
            updateDeletedImagesInput();
        }
    });
</script>

