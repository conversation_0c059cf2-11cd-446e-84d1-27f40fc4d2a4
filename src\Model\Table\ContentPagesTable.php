<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Database\Expression\QueryExpression;
use Cake\ORM\Locator\TableLocator;

/**
 * Suppliers Model
 *
 * @property \App\Model\Table\SupplierGroupsTable&\Cake\ORM\Association\BelongsTo $SupplierGroups
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\HasMany $Products
 * @property \App\Model\Table\SupplierCreditNotesTable&\Cake\ORM\Association\HasMany $SupplierCreditNotes
 * @property \App\Model\Table\SupplierPaymentsTable&\Cake\ORM\Association\HasMany $SupplierPayments
 * @property \App\Model\Table\SupplierPurchaseOrdersTable&\Cake\ORM\Association\HasMany $SupplierPurchaseOrders
 * @property \App\Model\Table\SupplierShowroomsTable&\Cake\ORM\Association\HasMany $SupplierShowrooms
 * @property \App\Model\Table\SupplierStocksTable&\Cake\ORM\Association\HasMany $SupplierStocks
 *
 * @method \App\Model\Entity\Supplier newEmptyEntity()
 * @method \App\Model\Entity\Supplier newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Supplier> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Supplier get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Supplier findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Supplier patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Supplier> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Supplier|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Supplier saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Supplier>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Supplier> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ContentPagesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('content_pages');
        $this->setDisplayField('title');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->hasMany('ContentImages', [
            'foreignKey' => 'content_page_id',
        ]);

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT'
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('content_category')
            ->maxLength('content_category', 255)
            ->requirePresence('content_category', 'create')
            ->notEmptyString('content_category');

        $validator
            ->scalar('content_category_identifier')
            ->maxLength('content_category_identifier', 255)
            ->requirePresence('content_category_identifier', 'create')
            ->notEmptyString('content_category_identifier');

        $validator
            ->scalar('title')
            ->maxLength('title', 255)
            ->requirePresence('title', 'create')
            ->notEmptyString('title');

        $validator
            ->scalar('title_ar')
            ->maxLength('title_ar', 255)
            ->requirePresence('title_ar', 'create')
            ->notEmptyString('title_ar');

        $validator
            ->scalar('tag')
            ->maxLength('tag', 255)
            ->requirePresence('tag', 'create')
            ->notEmptyString('tag')
            ->add('tag', 'unique', [
                'rule' => 'validateUnique',
                'provider' => 'table',
                'message' => 'This tag already exists. Please use a different tag name.'
            ]);

        $validator
            ->scalar('description')
            ->allowEmptyString('description');

        $validator
            ->scalar('description_ar')
            ->allowEmptyString('description_ar');

        // $validator
        //     ->scalar('image')
        //     ->maxLength('image', 255)
        //     ->requirePresence('image', 'create')
        //     ->allowEmptyString('image');

        $validator
            ->scalar('image_alt')
            ->maxLength('image_alt', 255)
            ->requirePresence('image_alt', 'create')
            ->allowEmptyString('image_alt');

        $validator
            ->scalar('content')
            ->notEmptyString('content');

        $validator
            ->scalar('content_ar')
            ->allowEmptyString('content_ar');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->scalar('published')
            ->notEmptyString('published');

        $validator
            ->scalar('published_date')
            ->notEmptyString('published_date');

        $validator
            ->scalar('url_key')
            ->maxLength('url_key', 255)
            ->allowEmptyString('url_key');

        return $validator;
    }
}
